<?php
$page_title = "تعديل العضو";
$require_admin = true;
require_once '../../includes/config.php';
require_once '../../includes/auth.php';
require_once '../../includes/functions.php';

// التحقق من وجود معرف العضو
if (!isset($_GET['id']) || !is_numeric($_GET['id'])) {
    $_SESSION['error'] = 'معرف العضو غير صحيح';
    redirect('index.php');
    exit;
}

$member_id = (int)$_GET['id'];

// منع تعديل المدير الحالي من قبل مديرين آخرين
if ($member_id == $_SESSION['user_id'] && isset($_POST['is_admin']) && !$_POST['is_admin']) {
    $_SESSION['error'] = 'لا يمكنك إزالة صلاحيات المدير من حسابك الخاص';
    redirect('index.php');
    exit;
}

// جلب بيانات العضو
$sql = "SELECT * FROM members WHERE id = ?";
$stmt = $conn->prepare($sql);
$stmt->bind_param("i", $member_id);
$stmt->execute();
$result = $stmt->get_result();

if ($result->num_rows === 0) {
    $_SESSION['error'] = 'العضو غير موجود';
    redirect('index.php');
    exit;
}

$member = $result->fetch_assoc();
$stmt->close();

$error = '';
$success = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // التحقق من CSRF token
    if (!verify_csrf_token($_POST['csrf_token'] ?? '')) {
        $error = 'رمز الأمان غير صحيح. يرجى المحاولة مرة أخرى.';
        log_security_event("CSRF token mismatch", "Edit member attempt for ID: $member_id");
    }
    // التحقق من معدل الطلبات
    elseif (!check_rate_limit('edit_member', 5, 60)) {
        $error = 'تم تجاوز الحد المسموح من المحاولات. يرجى الانتظار قليلاً.';
    }
    else {
        $name = clean_input($_POST['full_name'], 'string');
        $email = clean_input($_POST['email'], 'email');
        $phone = clean_input($_POST['phone_number'], 'string');
        $status = clean_input($_POST['status'], 'string');
        $is_admin = isset($_POST['is_admin']) ? 1 : 0;
        $join_date = clean_input($_POST['join_date'], 'string');
        $password = $_POST['password'] ?? '';
        $confirm_password = $_POST['confirm_password'] ?? '';

        // التحقق من صحة البيانات
        if (empty($name) || empty($email) || empty($join_date)) {
            $error = 'الحقول المطلوبة يجب تعبئتها';
        } elseif (!validate_email($email)) {
            $error = 'البريد الإلكتروني غير صحيح';
        } elseif (!empty($password) && $password !== $confirm_password) {
            $error = 'كلمتا المرور غير متطابقتين';
        } elseif (!empty($password)) {
            $password_errors = validate_password($password);
            if (!empty($password_errors)) {
                $error = implode('<br>', $password_errors);
            }
        } elseif (!in_array($status, ['active', 'inactive', 'suspended'])) {
            $error = 'حالة العضو غير صحيحة';
        } elseif ($member_id == $_SESSION['user_id'] && !$is_admin) {
            $error = 'لا يمكنك إزالة صلاحيات المدير من حسابك الخاص';
        } else {
        // التحقق من عدم وجود بريد إلكتروني مكرر (باستثناء العضو الحالي)
        $check_sql = "SELECT id FROM members WHERE email = ? AND id != ?";
        $check_stmt = $conn->prepare($check_sql);
        $check_stmt->bind_param("si", $email, $member_id);
        $check_stmt->execute();
        $check_stmt->store_result();

        if ($check_stmt->num_rows > 0) {
            $error = 'البريد الإلكتروني مسجل لعضو آخر';
        } else {
            // تحديث بيانات العضو
            if (!empty($password)) {
                // تحديث مع كلمة المرور
                $hashed_password = password_hash($password, PASSWORD_DEFAULT);
                $update_sql = "UPDATE members SET full_name = ?, email = ?, password = ?, phone_number = ?, join_date = ?, status = ?, is_admin = ? WHERE id = ?";
                $update_stmt = $conn->prepare($update_sql);
                $update_stmt->bind_param("ssssssii", $name, $email, $hashed_password, $phone, $join_date, $status, $is_admin, $member_id);
            } else {
                // تحديث بدون كلمة المرور
                $update_sql = "UPDATE members SET full_name = ?, email = ?, phone_number = ?, join_date = ?, status = ?, is_admin = ? WHERE id = ?";
                $update_stmt = $conn->prepare($update_sql);
                $update_stmt->bind_param("sssssii", $name, $email, $phone, $join_date, $status, $is_admin, $member_id);
            }

            if ($update_stmt->execute()) {
                $_SESSION['success'] = 'تم تحديث بيانات العضو بنجاح';

                // إذا كان العضو المحدث هو المستخدم الحالي، تحديث بيانات الجلسة
                if ($member_id == $_SESSION['user_id']) {
                    $_SESSION['user_name'] = $name;
                    $_SESSION['user_email'] = $email;
                    $_SESSION['is_admin'] = $is_admin;
                }

                redirect('index.php');
                exit;
            } else {
                $error = 'حدث خطأ أثناء تحديث البيانات: ' . $conn->error;
            }
            $update_stmt->close();
        }
        $check_stmt->close();
        }
    }
}

$nav_path = '../../';
include_once '../../includes/header.php';
?>

<div class="page-header">
    <h2>تعديل العضو: <?php echo htmlspecialchars($member['full_name']); ?></h2>
    <a href="index.php" class="btn" style="background: var(--gray-color);"><i class="fas fa-arrow-right"></i> العودة للقائمة</a>
</div>

<div class="form-container">
    <?php if ($error): ?>
        <div class="error"><?= $error ?></div>
    <?php elseif ($success): ?>
        <div class="success"><?= $success ?></div>
    <?php endif; ?>

    <form method="POST">
        <input type="hidden" name="csrf_token" value="<?php echo generate_csrf_token(); ?>">

        <div class="form-group">
            <label for="full_name">الاسم الكامل: *</label>
            <input type="text" id="full_name" name="full_name" value="<?php echo htmlspecialchars($member['full_name']); ?>" required>
        </div>

        <div class="form-group">
            <label for="email">البريد الإلكتروني: *</label>
            <input type="email" id="email" name="email" value="<?php echo htmlspecialchars($member['email']); ?>" required>
        </div>

        <div class="form-group">
            <label for="phone_number">رقم الهاتف:</label>
            <input type="tel" id="phone_number" name="phone_number" value="<?php echo htmlspecialchars($member['phone_number']); ?>">
        </div>

        <div class="form-group">
            <label for="join_date">تاريخ الانضمام: *</label>
            <input type="date" id="join_date" name="join_date" value="<?php echo $member['join_date']; ?>" required>
        </div>

        <div class="form-group">
            <label for="status">حالة العضو: *</label>
            <select id="status" name="status" required>
                <option value="active" <?php echo $member['status'] == 'active' ? 'selected' : ''; ?>>نشط</option>
                <option value="inactive" <?php echo $member['status'] == 'inactive' ? 'selected' : ''; ?>>غير نشط</option>
                <option value="suspended" <?php echo $member['status'] == 'suspended' ? 'selected' : ''; ?>>موقوف</option>
            </select>
        </div>

        <div class="form-group">
            <label>
                <input type="checkbox" name="is_admin" value="1" <?php echo $member['is_admin'] ? 'checked' : ''; ?>>
                منح صلاحيات المدير
            </label>
            <small style="color: var(--gray-color); display: block; margin-top: 5px;">
                تحذير: منح صلاحيات المدير يعطي العضو إمكانية الوصول الكامل للنظام
            </small>
        </div>

        <div class="form-group">
            <label for="password">كلمة المرور الجديدة (اتركها فارغة إذا لم ترغب في التغيير):</label>
            <input type="password" id="password" name="password">
            <small style="color: var(--gray-color); display: block; margin-top: 5px;">
                يجب أن تكون 6 أحرف على الأقل إذا تم تعبئتها
            </small>
        </div>

        <div class="form-group">
            <label for="confirm_password">تأكيد كلمة المرور الجديدة:</label>
            <input type="password" id="confirm_password" name="confirm_password">
        </div>

        <button type="submit" class="btn"><i class="fas fa-save"></i> حفظ التغييرات</button>
    </form>
</div>

<?php include_once '../../includes/footer.php'; ?>