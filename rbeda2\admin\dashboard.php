<?php
session_start();

// التحقق من تسجيل الدخول وصلاحيات المدير
if (!isset($_SESSION['user_id']) || !isset($_SESSION['is_admin']) || !$_SESSION['is_admin']) {
    header("Location: ../index.php");
    exit;
}

$page_title = "لوحة التحكم";
require_once '../includes/config.php';
require_once '../includes/functions.php';
require_once '../includes/logger.php';

// تسجيل دخول المدير إلى لوحة التحكم
log_activity('dashboard_access', 'Admin accessed dashboard');

// تهيئة المتغيرات بقيم افتراضية
$members_count = 0;
$active_members = 0;
$revenue = 0;
$monthly_revenue = 0;
$active_subscriptions = 0;
$expiring_subscriptions = 0;

// استعلامات محسنة للحصول على الإحصائيات مع معالجة الأخطاء
try {
    // استعلام واحد محسن للحصول على إحصائيات الأعضاء
    $members_sql = "SELECT
        COUNT(*) as total_members,
        SUM(CASE WHEN status = 'active' THEN 1 ELSE 0 END) as active_members
        FROM members";
    $members_result = mysqli_query($conn, $members_sql);
    if ($members_result) {
        $members_data = mysqli_fetch_assoc($members_result);
        $members_count = $members_data['total_members'];
        $active_members = $members_data['active_members'];
    }

    // استعلام واحد محسن للحصول على إحصائيات المدفوعات
    $payments_sql = "SELECT
        SUM(CASE WHEN status = 'completed' THEN amount ELSE 0 END) as total_revenue,
        SUM(CASE WHEN status = 'completed' AND MONTH(payment_date) = MONTH(CURDATE()) AND YEAR(payment_date) = YEAR(CURDATE()) THEN amount ELSE 0 END) as monthly_revenue
        FROM payments";
    $payments_result = mysqli_query($conn, $payments_sql);
    if ($payments_result) {
        $payments_data = mysqli_fetch_assoc($payments_result);
        $revenue = $payments_data['total_revenue'] ?? 0;
        $monthly_revenue = $payments_data['monthly_revenue'] ?? 0;
    }

    // استعلام واحد محسن للحصول على إحصائيات الاشتراكات
    $subscriptions_sql = "SELECT
        SUM(CASE WHEN status = 'active' THEN 1 ELSE 0 END) as active_subscriptions,
        SUM(CASE WHEN status = 'active' AND end_date BETWEEN CURDATE() AND DATE_ADD(CURDATE(), INTERVAL 7 DAY) THEN 1 ELSE 0 END) as expiring_subscriptions
        FROM subscriptions";
    $subscriptions_result = mysqli_query($conn, $subscriptions_sql);
    if ($subscriptions_result) {
        $subscriptions_data = mysqli_fetch_assoc($subscriptions_result);
        $active_subscriptions = $subscriptions_data['active_subscriptions'];
        $expiring_subscriptions = $subscriptions_data['expiring_subscriptions'];
    }

} catch (Exception $e) {
    // في حالة الخطأ، استخدم القيم الافتراضية
    error_log("خطأ في جلب الإحصائيات: " . $e->getMessage());
}

$nav_path = '../';
include_once '../includes/header.php';
?>

<!-- هيدر لوحة التحكم -->
<div class="dashboard-hero fade-in-up">
    <div class="hero-content">
        <div class="welcome-section">
            <div class="admin-avatar">
                <div class="avatar-icon">👨‍💼</div>
                <div class="status-indicator"></div>
            </div>
            <div class="welcome-text">
                <h1 class="welcome-title">مرحباً بعودتك، <span class="admin-name"><?php echo htmlspecialchars($_SESSION['user_name']); ?></span></h1>
                <p class="welcome-subtitle">لوحة التحكم الرئيسية - إدارة نادي أفانتي الرياضي</p>
                <div class="current-time">
                    <span class="time-icon">🕐</span>
                    <span id="currentTime"></span>
                </div>
            </div>
        </div>
        <div class="quick-actions">
            <a href="members/add.php" class="quick-action-btn">
                <span class="action-icon">👤➕</span>
                <span>إضافة عضو</span>
            </a>
            <a href="payments/add.php" class="quick-action-btn">
                <span class="action-icon">💰</span>
                <span>تسجيل دفعة</span>
            </a>
            <a href="reports/" class="quick-action-btn">
                <span class="action-icon">📊</span>
                <span>التقارير</span>
            </a>
        </div>
    </div>
</div>

<!-- بطاقات الإحصائيات المحسنة -->
<div class="dashboard-cards fade-in-up">
    <div class="card stats-card members-card" onclick="window.location.href='members/'" style="cursor: pointer;">
        <div class="card-background">
            <div class="card-pattern"></div>
        </div>
        <div class="card-content">
            <div class="card-header">
                <div class="card-icon">👥</div>
                <div class="card-menu">⋮</div>
            </div>
            <div class="card-body">
                <h3 class="card-title">إجمالي الأعضاء</h3>
                <div class="number pulse"><?php echo $members_count; ?></div>
                <div class="card-stats">
                    <div class="stat-item">
                        <span class="stat-label">النشطين</span>
                        <span class="stat-value active"><?php echo $active_members; ?></span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">غير النشطين</span>
                        <span class="stat-value inactive"><?php echo $members_count - $active_members; ?></span>
                    </div>
                </div>
            </div>
            <div class="card-footer">
                <div class="progress-bar">
                    <div class="progress-fill" style="width: <?php echo $members_count > 0 ? ($active_members / $members_count) * 100 : 0; ?>%"></div>
                </div>
                <small>اضغط لعرض التفاصيل</small>
            </div>
        </div>
    </div>

    <div class="card stats-card revenue-card" onclick="window.location.href='payments/'" style="cursor: pointer;">
        <div class="card-background">
            <div class="card-pattern"></div>
        </div>
        <div class="card-content">
            <div class="card-header">
                <div class="card-icon">💰</div>
                <div class="trend-indicator up">📈</div>
            </div>
            <div class="card-body">
                <h3 class="card-title">الإيرادات الشهرية</h3>
                <div class="number revenue-number"><?php echo number_format($monthly_revenue, 3); ?> <span class="currency">د.ل</span></div>
                <div class="card-stats">
                    <div class="stat-item">
                        <span class="stat-label">الإجمالي</span>
                        <span class="stat-value"><?php echo number_format($revenue, 3); ?> د.ل</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">متوسط يومي</span>
                        <span class="stat-value"><?php echo number_format($monthly_revenue / 30, 3); ?> د.ل</span>
                    </div>
                </div>
            </div>
            <div class="card-footer">
                <div class="revenue-chart">
                    <div class="chart-bar" style="height: 60%"></div>
                    <div class="chart-bar" style="height: 80%"></div>
                    <div class="chart-bar" style="height: 45%"></div>
                    <div class="chart-bar" style="height: 90%"></div>
                    <div class="chart-bar" style="height: 70%"></div>
                </div>
                <small>اضغط لعرض المدفوعات</small>
            </div>
        </div>
    </div>

    <div class="card stats-card subscriptions-card" onclick="window.location.href='subscriptions/'" style="cursor: pointer;">
        <div class="card-background">
            <div class="card-pattern"></div>
        </div>
        <div class="card-content">
            <div class="card-header">
                <div class="card-icon">🎫</div>
                <div class="notification-badge"><?php echo $expiring_subscriptions; ?></div>
            </div>
            <div class="card-body">
                <h3 class="card-title">الاشتراكات النشطة</h3>
                <div class="number"><?php echo $active_subscriptions; ?></div>
                <div class="card-stats">
                    <div class="stat-item">
                        <span class="stat-label">تنتهي قريباً</span>
                        <span class="stat-value warning"><?php echo $expiring_subscriptions; ?></span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">مستقرة</span>
                        <span class="stat-value stable"><?php echo $active_subscriptions - $expiring_subscriptions; ?></span>
                    </div>
                </div>
            </div>
            <div class="card-footer">
                <div class="subscription-status">
                    <div class="status-dot active"></div>
                    <div class="status-dot warning"></div>
                    <div class="status-dot inactive"></div>
                </div>
                <small>اضغط لإدارة الاشتراكات</small>
            </div>
        </div>
    </div>

    <div class="card stats-card alert-card" onclick="window.location.href='subscriptions/?filter=expiring'" style="cursor: pointer;">
        <div class="card-background alert-bg">
            <div class="card-pattern"></div>
        </div>
        <div class="card-content">
            <div class="card-header">
                <div class="card-icon warning-icon">⚠️</div>
                <div class="urgency-indicator">🔥</div>
            </div>
            <div class="card-body">
                <h3 class="card-title">تحتاج متابعة</h3>
                <div class="number warning-number"><?php echo $expiring_subscriptions; ?></div>
                <div class="card-stats">
                    <div class="stat-item">
                        <span class="stat-label">خلال 7 أيام</span>
                        <span class="stat-value urgent"><?php echo $expiring_subscriptions; ?></span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">تحتاج تجديد</span>
                        <span class="stat-value"><?php echo $expiring_subscriptions; ?></span>
                    </div>
                </div>
            </div>
            <div class="card-footer">
                <div class="alert-timeline">
                    <div class="timeline-dot active"></div>
                    <div class="timeline-line"></div>
                    <div class="timeline-dot warning"></div>
                    <div class="timeline-line"></div>
                    <div class="timeline-dot danger"></div>
                </div>
                <small>اضغط لعرض الاشتراكات المنتهية</small>
            </div>
        </div>
    </div>
</div>

<!-- قسم الأعضاء الجدد -->
<div class="section-header fade-in-up">
    <div class="section-title">
        <h2>آخر الأعضاء المسجلين</h2>
        <p>أحدث الانضمامات إلى النادي</p>
    </div>
    <div class="section-actions">
        <a href="members/" class="btn btn-primary">
            <span>عرض الكل</span>
            <div class="btn-icon">👥</div>
        </a>
        <a href="members/add.php" class="btn btn-secondary">
            <span>إضافة عضو</span>
            <div class="btn-icon">➕</div>
        </a>
    </div>
</div>

<div class="enhanced-table-container fade-in-up">
    <div class="table-header">
        <div class="table-title">
            <span class="table-icon">📋</span>
            <span>قائمة الأعضاء الجدد</span>
        </div>
        <div class="table-filters">
            <select class="filter-select">
                <option value="all">جميع الحالات</option>
                <option value="active">نشط</option>
                <option value="inactive">غير نشط</option>
            </select>
        </div>
    </div>

    <div class="table-wrapper">
        <table class="modern-table">
            <thead>
                <tr>
                    <th>
                        <div class="th-content">
                            <span>الاسم</span>
                            <span class="sort-icon">↕️</span>
                        </div>
                    </th>
                    <th>
                        <div class="th-content">
                            <span>البريد الإلكتروني</span>
                            <span class="sort-icon">↕️</span>
                        </div>
                    </th>
                    <th>
                        <div class="th-content">
                            <span>رقم الهاتف</span>
                            <span class="sort-icon">↕️</span>
                        </div>
                    </th>
                    <th>
                        <div class="th-content">
                            <span>تاريخ الانضمام</span>
                            <span class="sort-icon">↕️</span>
                        </div>
                    </th>
                    <th>
                        <div class="th-content">
                            <span>الحالة</span>
                            <span class="sort-icon">↕️</span>
                        </div>
                    </th>
                    <th>الإجراءات</th>
                </tr>
            </thead>
            <tbody>
                <?php
                // استعلام محسن مع تحديد الحقول المطلوبة فقط
                $recent_members_sql = "SELECT id, full_name, email, phone_number, join_date, status
                                      FROM members
                                      ORDER BY join_date DESC
                                      LIMIT 5";
                $recent_members_stmt = $conn->prepare($recent_members_sql);
                $recent_members_stmt->execute();
                $recent_members_result = $recent_members_stmt->get_result();

                if ($recent_members_result && $recent_members_result->num_rows > 0) {
                    while ($row = $recent_members_result->fetch_assoc()) {
                        $status_text = match($row['status']) {
                            'active' => 'نشط',
                            'inactive' => 'غير نشط',
                            'suspended' => 'موقوف',
                            default => 'غير محدد'
                        };

                        $status_class = match($row['status']) {
                            'active' => 'status-active',
                            'inactive' => 'status-inactive',
                            'suspended' => 'status-suspended',
                            default => 'status-unknown'
                        };

                        $join_date = new DateTime($row['join_date']);
                        $now = new DateTime();
                        $diff = $now->diff($join_date);
                        $days_ago = $diff->days;

                        echo "<tr class='table-row' data-member-id='{$row['id']}'>
                            <td>
                                <div class='member-info'>
                                    <div class='member-avatar'>
                                        <span>" . substr($row['full_name'], 0, 1) . "</span>
                                    </div>
                                    <div class='member-details'>
                                        <div class='member-name'>" . htmlspecialchars($row['full_name']) . "</div>
                                        <div class='member-id'>ID: " . $row['id'] . "</div>
                                    </div>
                                </div>
                            </td>
                            <td>
                                <div class='email-cell'>
                                    <span class='email-icon'>📧</span>
                                    <span class='email-text'>" . htmlspecialchars($row['email']) . "</span>
                                </div>
                            </td>
                            <td>
                                <div class='phone-cell'>
                                    <span class='phone-icon'>📱</span>
                                    <span class='phone-text'>" . htmlspecialchars($row['phone_number'] ?? 'غير محدد') . "</span>
                                </div>
                            </td>
                            <td>
                                <div class='date-cell'>
                                    <div class='date-main'>" . $join_date->format('Y-m-d') . "</div>
                                    <div class='date-relative'>منذ {$days_ago} يوم</div>
                                </div>
                            </td>
                            <td>
                                <span class='status-badge {$status_class}'>
                                    <span class='status-dot'></span>
                                    <span class='status-text'>{$status_text}</span>
                                </span>
                            </td>
                            <td>
                                <div class='action-buttons'>
                                    <button class='action-btn view-btn' onclick=\"window.location.href='members/profile.php?id={$row['id']}'\" title='عرض الملف الشخصي'>
                                        👁️
                                    </button>
                                    <button class='action-btn edit-btn' onclick=\"window.location.href='members/edit.php?id={$row['id']}'\" title='تعديل'>
                                        ✏️
                                    </button>
                                    <button class='action-btn message-btn' onclick='sendMessage({$row['id']})' title='إرسال رسالة'>
                                        💬
                                    </button>
                                </div>
                            </td>
                        </tr>";
                    }
                } else {
                    echo "<tr class='empty-row'>
                            <td colspan='6'>
                                <div class='empty-state'>
                                    <div class='empty-icon'>👥</div>
                                    <div class='empty-title'>لا توجد أعضاء مسجلين بعد</div>
                                    <div class='empty-subtitle'>ابدأ بإضافة أول عضو في النادي</div>
                                    <a href='members/add.php' class='btn btn-primary empty-action'>
                                        <span>إضافة عضو جديد</span>
                                        <div class='btn-icon'>➕</div>
                                    </a>
                                </div>
                            </td>
                        </tr>";
                }
                $recent_members_stmt->close();
                ?>
            </tbody>
        </table>
    </div>

    <div class="table-footer">
        <div class="table-info">
            <span>عرض آخر 5 أعضاء من إجمالي <?php echo $members_count; ?> عضو</span>
        </div>
        <div class="table-pagination">
            <a href="members/" class="pagination-link">عرض المزيد</a>
        </div>
    </div>
</div>

<style>
/* تصميم لوحة التحكم المحسن */
.dashboard-hero {
    background: var(--gradient-primary);
    border-radius: 24px;
    padding: 3rem;
    margin-bottom: 3rem;
    color: white;
    position: relative;
    overflow: hidden;
}

.dashboard-hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="white" stroke-width="0.5" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
}

.hero-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: relative;
    z-index: 2;
}

.welcome-section {
    display: flex;
    align-items: center;
    gap: 2rem;
}

.admin-avatar {
    position: relative;
    width: 80px;
    height: 80px;
    background: rgba(255,255,255,0.2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    backdrop-filter: blur(10px);
    border: 3px solid rgba(255,255,255,0.3);
}

.avatar-icon {
    font-size: 2.5rem;
}

.status-indicator {
    position: absolute;
    bottom: 5px;
    right: 5px;
    width: 20px;
    height: 20px;
    background: #10b981;
    border-radius: 50%;
    border: 3px solid white;
    animation: pulse 2s ease-in-out infinite;
}

.welcome-title {
    font-size: 2.5rem;
    font-weight: 800;
    margin-bottom: 0.5rem;
    text-shadow: 0 2px 4px rgba(0,0,0,0.3);
}

.admin-name {
    background: linear-gradient(45deg, #fbbf24, #f59e0b);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.welcome-subtitle {
    font-size: 1.2rem;
    opacity: 0.9;
    margin-bottom: 1rem;
}

.current-time {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 1rem;
    opacity: 0.8;
}

.quick-actions {
    display: flex;
    gap: 1rem;
}

.quick-action-btn {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.5rem;
    padding: 1.5rem 1rem;
    background: rgba(255,255,255,0.1);
    border-radius: 16px;
    text-decoration: none;
    color: white;
    transition: var(--transition-normal);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255,255,255,0.2);
    min-width: 120px;
}

.quick-action-btn:hover {
    background: rgba(255,255,255,0.2);
    transform: translateY(-3px);
}

.action-icon {
    font-size: 2rem;
}

/* بطاقات الإحصائيات المحسنة */
.stats-card {
    position: relative;
    overflow: hidden;
    border: 1px solid rgba(59, 130, 246, 0.1);
}

.card-background {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    opacity: 0.05;
}

.card-pattern {
    width: 100%;
    height: 100%;
    background: var(--gradient-primary);
    transform: rotate(45deg) scale(1.5);
}

.card-content {
    position: relative;
    z-index: 2;
    height: 100%;
    display: flex;
    flex-direction: column;
}

.card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
}

.card-icon {
    font-size: 2.5rem;
    opacity: 0.8;
}

.card-menu {
    font-size: 1.5rem;
    color: var(--gray-color);
    cursor: pointer;
    transition: var(--transition-normal);
}

.card-menu:hover {
    color: var(--primary-color);
}

.card-body {
    flex: 1;
}

.card-title {
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--gray-color);
    margin-bottom: 1rem;
}

.card-stats {
    display: flex;
    justify-content: space-between;
    margin-bottom: 1rem;
}

.stat-item {
    text-align: center;
}

.stat-label {
    display: block;
    font-size: 0.8rem;
    color: var(--gray-color);
    margin-bottom: 0.3rem;
}

.stat-value {
    font-size: 1.2rem;
    font-weight: 700;
}

.stat-value.active { color: var(--success-color); }
.stat-value.inactive { color: var(--gray-color); }
.stat-value.warning { color: var(--warning-color); }
.stat-value.stable { color: var(--info-color); }
.stat-value.urgent { color: var(--danger-color); }

.progress-bar {
    width: 100%;
    height: 6px;
    background: rgba(59, 130, 246, 0.1);
    border-radius: 3px;
    overflow: hidden;
    margin-bottom: 0.5rem;
}

.progress-fill {
    height: 100%;
    background: var(--gradient-primary);
    border-radius: 3px;
    transition: var(--transition-normal);
}

.trend-indicator {
    padding: 0.3rem 0.6rem;
    background: rgba(16, 185, 129, 0.1);
    border-radius: 8px;
    font-size: 0.8rem;
    color: var(--success-color);
}

.notification-badge {
    background: var(--danger-color);
    color: white;
    border-radius: 50%;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.8rem;
    font-weight: 700;
}

.revenue-chart {
    display: flex;
    align-items: end;
    gap: 4px;
    height: 30px;
    margin-bottom: 0.5rem;
}

.chart-bar {
    flex: 1;
    background: var(--gradient-primary);
    border-radius: 2px;
    min-height: 4px;
    animation: chartGrow 1s ease-out;
}

@keyframes chartGrow {
    from { height: 0; }
    to { height: var(--height); }
}

.subscription-status {
    display: flex;
    gap: 8px;
    margin-bottom: 0.5rem;
}

.status-dot {
    width: 12px;
    height: 12px;
    border-radius: 50%;
}

.status-dot.active { background: var(--success-color); }
.status-dot.warning { background: var(--warning-color); }
.status-dot.inactive { background: var(--gray-color); }

.alert-bg {
    background: linear-gradient(135deg, rgba(245, 158, 11, 0.1) 0%, rgba(239, 68, 68, 0.1) 100%);
}

.warning-icon {
    animation: bounce 2s ease-in-out infinite;
}

@keyframes bounce {
    0%, 100% { transform: translateY(0); }
    50% { transform: translateY(-5px); }
}

.urgency-indicator {
    animation: pulse 1.5s ease-in-out infinite;
}

.alert-timeline {
    display: flex;
    align-items: center;
    gap: 4px;
    margin-bottom: 0.5rem;
}

.timeline-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
}

.timeline-dot.active { background: var(--success-color); }
.timeline-dot.warning { background: var(--warning-color); }
.timeline-dot.danger { background: var(--danger-color); }

.timeline-line {
    flex: 1;
    height: 2px;
    background: var(--gray-color);
    opacity: 0.3;
}

/* قسم الجدول المحسن */
.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin: 3rem 0 2rem 0;
    padding: 2rem;
    background: var(--white);
    border-radius: 20px;
    box-shadow: var(--shadow-light);
    border: 1px solid rgba(59, 130, 246, 0.1);
}

.section-title h2 {
    font-size: 2rem;
    font-weight: 800;
    color: var(--primary-color);
    margin-bottom: 0.5rem;
}

.section-title p {
    color: var(--gray-color);
    font-size: 1rem;
}

.section-actions {
    display: flex;
    gap: 1rem;
}

.enhanced-table-container {
    background: var(--white);
    border-radius: 24px;
    box-shadow: var(--shadow-medium);
    overflow: hidden;
    border: 1px solid rgba(59, 130, 246, 0.1);
}

.table-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1.5rem 2rem;
    background: var(--gradient-light);
    border-bottom: 1px solid rgba(59, 130, 246, 0.1);
}

.table-title {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 1.2rem;
    font-weight: 700;
    color: var(--primary-color);
}

.table-icon {
    font-size: 1.5rem;
}

.table-filters {
    display: flex;
    gap: 1rem;
}

.filter-select {
    padding: 0.5rem 1rem;
    border: 1px solid rgba(59, 130, 246, 0.2);
    border-radius: 8px;
    background: var(--white);
    color: var(--primary-color);
    font-weight: 500;
}

.table-wrapper {
    overflow-x: auto;
}

.modern-table {
    width: 100%;
    border-collapse: collapse;
}

.modern-table th {
    background: var(--gradient-primary);
    color: white;
    padding: 1.5rem 1rem;
    text-align: right;
    font-weight: 700;
    position: relative;
}

.th-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.sort-icon {
    opacity: 0.7;
    cursor: pointer;
    transition: var(--transition-normal);
}

.sort-icon:hover {
    opacity: 1;
    transform: scale(1.2);
}

.table-row {
    transition: var(--transition-normal);
    border-bottom: 1px solid rgba(59, 130, 246, 0.08);
}

.table-row:hover {
    background: linear-gradient(90deg, rgba(59, 130, 246, 0.03) 0%, rgba(6, 182, 212, 0.02) 100%);
    transform: translateX(5px);
}

.modern-table td {
    padding: 1.5rem 1rem;
    vertical-align: middle;
}

.member-info {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.member-avatar {
    width: 45px;
    height: 45px;
    background: var(--gradient-primary);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: 700;
    font-size: 1.2rem;
}

.member-details {
    flex: 1;
}

.member-name {
    font-weight: 600;
    color: var(--dark-color);
    margin-bottom: 0.2rem;
}

.member-id {
    font-size: 0.8rem;
    color: var(--gray-color);
}

.email-cell, .phone-cell {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.email-icon, .phone-icon {
    font-size: 1.2rem;
    opacity: 0.7;
}

.date-cell {
    text-align: center;
}

.date-main {
    font-weight: 600;
    color: var(--dark-color);
    margin-bottom: 0.2rem;
}

.date-relative {
    font-size: 0.8rem;
    color: var(--gray-color);
}

.status-badge {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
}

.status-badge .status-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
}

.status-active {
    background: rgba(16, 185, 129, 0.1);
    color: var(--success-color);
}

.status-active .status-dot {
    background: var(--success-color);
}

.status-inactive {
    background: rgba(107, 114, 128, 0.1);
    color: var(--gray-color);
}

.status-inactive .status-dot {
    background: var(--gray-color);
}

.status-suspended {
    background: rgba(239, 68, 68, 0.1);
    color: var(--danger-color);
}

.status-suspended .status-dot {
    background: var(--danger-color);
}

.action-buttons {
    display: flex;
    gap: 0.5rem;
}

.action-btn {
    width: 35px;
    height: 35px;
    border: none;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: var(--transition-normal);
    font-size: 1rem;
}

.view-btn {
    background: rgba(59, 130, 246, 0.1);
    color: var(--primary-color);
}

.view-btn:hover {
    background: var(--primary-color);
    color: white;
}

.edit-btn {
    background: rgba(245, 158, 11, 0.1);
    color: var(--warning-color);
}

.edit-btn:hover {
    background: var(--warning-color);
    color: white;
}

.message-btn {
    background: rgba(16, 185, 129, 0.1);
    color: var(--success-color);
}

.message-btn:hover {
    background: var(--success-color);
    color: white;
}

.empty-state {
    text-align: center;
    padding: 4rem 2rem;
}

.empty-icon {
    font-size: 4rem;
    margin-bottom: 1rem;
    opacity: 0.5;
}

.empty-title {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--gray-color);
    margin-bottom: 0.5rem;
}

.empty-subtitle {
    color: var(--gray-color);
    margin-bottom: 2rem;
}

.table-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1.5rem 2rem;
    background: var(--gradient-light);
    border-top: 1px solid rgba(59, 130, 246, 0.1);
}

.table-info {
    color: var(--gray-color);
    font-size: 0.9rem;
}

.pagination-link {
    color: var(--primary-color);
    text-decoration: none;
    font-weight: 600;
    transition: var(--transition-normal);
}

.pagination-link:hover {
    color: var(--secondary-color);
}

/* الاستجابة */
@media (max-width: 1024px) {
    .hero-content {
        flex-direction: column;
        gap: 2rem;
        text-align: center;
    }

    .quick-actions {
        justify-content: center;
    }
}

@media (max-width: 768px) {
    .dashboard-hero {
        padding: 2rem;
    }

    .welcome-title {
        font-size: 2rem;
    }

    .section-header {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }

    .section-actions {
        justify-content: center;
    }

    .table-header {
        flex-direction: column;
        gap: 1rem;
    }

    .member-info {
        flex-direction: column;
        text-align: center;
        gap: 0.5rem;
    }

    .action-buttons {
        justify-content: center;
    }
}
</style>

<script>
// تأثيرات تفاعلية محسنة للوحة التحكم
document.addEventListener('DOMContentLoaded', function() {
    // تحديث الوقت الحالي
    function updateCurrentTime() {
        const now = new Date();
        const timeString = now.toLocaleString('ar-EG', {
            weekday: 'long',
            year: 'numeric',
            month: 'long',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
        });
        const timeElement = document.getElementById('currentTime');
        if (timeElement) {
            timeElement.textContent = timeString;
        }
    }

    // تحديث الوقت كل ثانية
    updateCurrentTime();
    setInterval(updateCurrentTime, 1000);

    // تأثيرات البطاقات
    const statsCards = document.querySelectorAll('.stats-card');
    statsCards.forEach((card, index) => {
        // تأثير التحميل المتدرج
        card.style.animationDelay = `${index * 0.1}s`;
        card.classList.add('fade-in-up');

        // تأثير النقر
        card.addEventListener('click', function() {
            this.style.transform = 'scale(0.98)';
            setTimeout(() => {
                this.style.transform = '';
            }, 150);
        });

        // تأثير التمرير
        card.addEventListener('mouseenter', function() {
            const pattern = this.querySelector('.card-pattern');
            if (pattern) {
                pattern.style.transform = 'rotate(45deg) scale(1.8)';
                pattern.style.opacity = '0.1';
            }
        });

        card.addEventListener('mouseleave', function() {
            const pattern = this.querySelector('.card-pattern');
            if (pattern) {
                pattern.style.transform = 'rotate(45deg) scale(1.5)';
                pattern.style.opacity = '0.05';
            }
        });
    });

    // تأثيرات الجدول
    const tableRows = document.querySelectorAll('.table-row');
    tableRows.forEach((row, index) => {
        // تأثير التحميل المتدرج
        row.style.animationDelay = `${index * 0.05}s`;
        row.classList.add('fade-in-right');

        // تأثير النقر على الصف
        row.addEventListener('click', function() {
            const memberId = this.dataset.memberId;
            if (memberId) {
                window.location.href = `members/profile.php?id=${memberId}`;
            }
        });
    });

    // تأثيرات الأزرار
    const actionButtons = document.querySelectorAll('.action-btn');
    actionButtons.forEach(btn => {
        btn.addEventListener('click', function(e) {
            e.stopPropagation(); // منع تفعيل النقر على الصف

            // تأثير الموجة
            const ripple = document.createElement('span');
            ripple.classList.add('ripple');
            this.appendChild(ripple);

            setTimeout(() => {
                ripple.remove();
            }, 600);
        });
    });

    // تحديث الإحصائيات تلقائياً
    let updateInterval;
    function startStatsUpdate() {
        updateInterval = setInterval(async function() {
            try {
                const response = await fetch('api/dashboard-stats.php');
                if (response.ok) {
                    const data = await response.json();
                    updateStatsDisplay(data);
                }
            } catch (error) {
                console.log('خطأ في تحديث الإحصائيات:', error);
            }
        }, 30000); // كل 30 ثانية
    }

    function updateStatsDisplay(data) {
        // تحديث أرقام البطاقات بتأثير متحرك
        const numbers = document.querySelectorAll('.number');
        numbers.forEach((numberEl, index) => {
            const newValue = Object.values(data)[index];
            if (newValue !== undefined) {
                animateNumber(numberEl, parseInt(numberEl.textContent), newValue);
            }
        });
    }

    function animateNumber(element, start, end) {
        const duration = 1000;
        const startTime = performance.now();

        function update(currentTime) {
            const elapsed = currentTime - startTime;
            const progress = Math.min(elapsed / duration, 1);

            const current = Math.floor(start + (end - start) * progress);
            element.textContent = current.toLocaleString();

            if (progress < 1) {
                requestAnimationFrame(update);
            }
        }

        requestAnimationFrame(update);
    }

    // بدء تحديث الإحصائيات
    startStatsUpdate();

    // إيقاف التحديث عند مغادرة الصفحة
    window.addEventListener('beforeunload', function() {
        if (updateInterval) {
            clearInterval(updateInterval);
        }
    });

    // تأثيرات إضافية
    const quickActionBtns = document.querySelectorAll('.quick-action-btn');
    quickActionBtns.forEach(btn => {
        btn.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-5px) scale(1.05)';
        });

        btn.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(-3px) scale(1)';
        });
    });

    // تأثير البحث في الجدول
    const filterSelect = document.querySelector('.filter-select');
    if (filterSelect) {
        filterSelect.addEventListener('change', function() {
            const filterValue = this.value;
            const rows = document.querySelectorAll('.table-row');

            rows.forEach(row => {
                const statusBadge = row.querySelector('.status-badge');
                if (statusBadge) {
                    const shouldShow = filterValue === 'all' ||
                                     statusBadge.classList.contains(`status-${filterValue}`);

                    if (shouldShow) {
                        row.style.display = '';
                        row.style.animation = 'fadeInRight 0.3s ease-out';
                    } else {
                        row.style.display = 'none';
                    }
                }
            });
        });
    }
});

// وظائف مساعدة
function sendMessage(memberId) {
    // يمكن تطوير هذه الوظيفة لاحقاً
    alert(`إرسال رسالة للعضو رقم: ${memberId}`);
}

// إضافة تأثير الموجة للأزرار
const style = document.createElement('style');
style.textContent = `
    .ripple {
        position: absolute;
        border-radius: 50%;
        background: rgba(255, 255, 255, 0.6);
        transform: scale(0);
        animation: ripple-animation 0.6s linear;
        pointer-events: none;
    }

    @keyframes ripple-animation {
        to {
            transform: scale(4);
            opacity: 0;
        }
    }
`;
document.head.appendChild(style);
</script>

<?php include_once '../includes/footer.php'; ?>