<?php
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);
session_start();
require_once 'includes/config.php';
require_once 'includes/functions.php';

if (is_logged_in()) {
    redirect(is_admin() ? 'admin/dashboard.php' : 'member/dashboard.php');
    exit;
}

$error = '';
$success = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $name = sanitize_input($_POST['full_name']);
    $email = sanitize_input($_POST['email']);
    $phone = sanitize_input($_POST['phone_number']);
    $password = sanitize_input($_POST['password']);
    $confirm_password = sanitize_input($_POST['confirm_password']);
    $join_date = date('Y-m-d');

    // التحقق من صحة البيانات
    if (empty($name) || empty($email) || empty($password) || empty($confirm_password)) {
        $error = 'جميع الحقول المطلوبة يجب تعبئتها';
    } elseif ($password !== $confirm_password) {
        $error = 'كلمتا المرور غير متطابقتين';
    } else {
        // التحقق من عدم وجود بريد إلكتروني مكرر
        $check_sql = "SELECT id FROM members WHERE email = ?";
        $check_stmt = $conn->prepare($check_sql);
        $check_stmt->bind_param("s", $email);
        $check_stmt->execute();
        $check_stmt->store_result();

        if ($check_stmt->num_rows > 0) {
            $error = 'البريد الإلكتروني مسجل مسبقاً';
        } else {
            // تشفير كلمة المرور
            $hashed_password = password_hash($password, PASSWORD_DEFAULT);

            // إدخال العضو الجديد
            $insert_sql = "INSERT INTO members (full_name, email, password, phone_number, join_date, status, is_admin)
                          VALUES (?, ?, ?, ?, ?, 'active', 0)";
            $insert_stmt = $conn->prepare($insert_sql);
            $insert_stmt->bind_param("sssss", $name, $email, $hashed_password, $phone, $join_date);

            if ($insert_stmt->execute()) {
                $success = 'تم تسجيل العضو بنجاح! يمكنك الآن <a href="index.php">تسجيل الدخول</a>';
            } else {
                $error = 'حدث خطأ أثناء التسجيل: ' . $conn->error;
            }
        }

        $check_stmt->close();
    }
}
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>انضم إلينا - نادي أفانتي الرياضي</title>
    <link rel="stylesheet" href="assets/css/modern-style.css">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700;800;900&display=swap" rel="stylesheet">
    <link rel="icon" href="assets/images/logo.png" type="image/png">
</head>
<body class="register-page">
    <!-- خلفية متحركة -->
    <div class="animated-bg">
        <div class="floating-shapes">
            <div class="shape shape-1"></div>
            <div class="shape shape-2"></div>
            <div class="shape shape-3"></div>
            <div class="shape shape-4"></div>
            <div class="shape shape-5"></div>
            <div class="shape shape-6"></div>
        </div>
    </div>

    <!-- الحاوي الرئيسي -->
    <div class="register-wrapper">
        <!-- قسم المعلومات -->
        <div class="info-section fade-in-right">
            <div class="logo-container">
                <div class="logo-icon">🏆</div>
                <h1 class="logo-text">انضم إلى أفانتي</h1>
                <p class="logo-subtitle">ابدأ رحلتك الرياضية معنا</p>
            </div>

            <div class="benefits-list">
                <div class="benefit-item">
                    <div class="benefit-icon">💪</div>
                    <div class="benefit-text">
                        <h3>تدريب شخصي</h3>
                        <p>برامج مخصصة تناسب مستواك وأهدافك</p>
                    </div>
                </div>

                <div class="benefit-item">
                    <div class="benefit-icon">🎯</div>
                    <div class="benefit-text">
                        <h3>أهداف واضحة</h3>
                        <p>خطط تدريبية مدروسة لتحقيق النتائج</p>
                    </div>
                </div>

                <div class="benefit-item">
                    <div class="benefit-icon">🏅</div>
                    <div class="benefit-text">
                        <h3>إنجازات مميزة</h3>
                        <p>تتبع تقدمك واحتفل بإنجازاتك</p>
                    </div>
                </div>

                <div class="benefit-item">
                    <div class="benefit-icon">🤝</div>
                    <div class="benefit-text">
                        <h3>مجتمع داعم</h3>
                        <p>انضم لعائلة رياضية متحمسة</p>
                    </div>
                </div>
            </div>

            <!-- شهادات الأعضاء -->
            <div class="testimonial">
                <div class="testimonial-content">
                    <p>"أفضل قرار اتخذته في حياتي الرياضية"</p>
                    <div class="testimonial-author">
                        <strong>أحمد محمد</strong>
                        <span>عضو منذ 2022</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- قسم التسجيل -->
        <div class="register-section fade-in-left">
            <div class="register-container">
                <div class="register-header">
                    <h2 class="register-title">إنشاء حساب جديد</h2>
                    <p class="register-subtitle">املأ البيانات التالية للانضمام إلينا</p>
                </div>

                <?php if ($error): ?>
                    <div class="alert alert-error">
                        <span><?= $error ?></span>
                    </div>
                <?php elseif ($success): ?>
                    <div class="alert alert-success">
                        <span><?= $success ?></span>
                    </div>
                <?php endif; ?>

                <form method="POST" class="register-form" id="registerForm">
                    <div class="form-row">
                        <div class="form-group form-floating">
                            <input type="text" id="full_name" name="full_name" placeholder=" " required>
                            <label for="full_name">الاسم الكامل</label>
                            <div class="input-icon">👤</div>
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group form-floating">
                            <input type="email" id="email" name="email" placeholder=" " required>
                            <label for="email">البريد الإلكتروني</label>
                            <div class="input-icon">📧</div>
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group form-floating">
                            <input type="tel" id="phone_number" name="phone_number" placeholder=" ">
                            <label for="phone_number">رقم الهاتف (اختياري)</label>
                            <div class="input-icon">📱</div>
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group form-floating">
                            <input type="password" id="password" name="password" placeholder=" " required>
                            <label for="password">كلمة المرور</label>
                            <div class="input-icon">🔒</div>
                            <button type="button" class="toggle-password" onclick="togglePassword('password')">👁️</button>
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group form-floating">
                            <input type="password" id="confirm_password" name="confirm_password" placeholder=" " required>
                            <label for="confirm_password">تأكيد كلمة المرور</label>
                            <div class="input-icon">🔐</div>
                            <button type="button" class="toggle-password" onclick="togglePassword('confirm_password')">👁️</button>
                        </div>
                    </div>

                    <!-- مؤشر قوة كلمة المرور -->
                    <div class="password-strength">
                        <div class="strength-label">قوة كلمة المرور:</div>
                        <div class="strength-bar">
                            <div class="strength-fill" id="strengthFill"></div>
                        </div>
                        <div class="strength-text" id="strengthText">ضعيفة</div>
                    </div>

                    <div class="form-options">
                        <label class="checkbox-container">
                            <input type="checkbox" name="terms" required>
                            <span class="checkmark"></span>
                            أوافق على <a href="#" class="terms-link">الشروط والأحكام</a>
                        </label>
                    </div>

                    <div class="form-options">
                        <label class="checkbox-container">
                            <input type="checkbox" name="newsletter">
                            <span class="checkmark"></span>
                            أريد تلقي النشرة الإخبارية والعروض الخاصة
                        </label>
                    </div>

                    <button type="submit" class="btn btn-primary btn-register">
                        <span>إنشاء الحساب</span>
                        <div class="btn-icon">🚀</div>
                    </button>
                </form>

                <div class="register-footer">
                    <p>لديك حساب بالفعل؟ <a href="index.php" class="login-link">تسجيل الدخول</a></p>
                </div>

                <!-- مزايا العضوية -->
                <div class="membership-benefits">
                    <h4>مزايا العضوية</h4>
                    <div class="benefits-grid">
                        <div class="benefit-badge">
                            <span class="badge-icon">🎁</span>
                            <span class="badge-text">أسبوع مجاني</span>
                        </div>
                        <div class="benefit-badge">
                            <span class="badge-icon">📊</span>
                            <span class="badge-text">تتبع التقدم</span>
                        </div>
                        <div class="benefit-badge">
                            <span class="badge-icon">💬</span>
                            <span class="badge-text">دعم 24/7</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // تأثيرات تفاعلية
        function togglePassword(inputId) {
            const passwordInput = document.getElementById(inputId);
            const toggleBtn = passwordInput.parentElement.querySelector('.toggle-password');

            if (passwordInput.type === 'password') {
                passwordInput.type = 'text';
                toggleBtn.textContent = '🙈';
            } else {
                passwordInput.type = 'password';
                toggleBtn.textContent = '👁️';
            }
        }

        // مؤشر قوة كلمة المرور
        function checkPasswordStrength(password) {
            let strength = 0;
            let text = 'ضعيفة';
            let color = '#ef4444';

            if (password.length >= 8) strength++;
            if (/[a-z]/.test(password)) strength++;
            if (/[A-Z]/.test(password)) strength++;
            if (/[0-9]/.test(password)) strength++;
            if (/[^A-Za-z0-9]/.test(password)) strength++;

            switch (strength) {
                case 0:
                case 1:
                    text = 'ضعيفة جداً';
                    color = '#ef4444';
                    break;
                case 2:
                    text = 'ضعيفة';
                    color = '#f59e0b';
                    break;
                case 3:
                    text = 'متوسطة';
                    color = '#eab308';
                    break;
                case 4:
                    text = 'قوية';
                    color = '#22c55e';
                    break;
                case 5:
                    text = 'قوية جداً';
                    color = '#10b981';
                    break;
            }

            return { strength: (strength / 5) * 100, text, color };
        }

        // تأثيرات الحركة
        document.addEventListener('DOMContentLoaded', function() {
            // تأثير الكتابة
            const inputs = document.querySelectorAll('input');
            inputs.forEach(input => {
                input.addEventListener('focus', function() {
                    this.parentElement.classList.add('focused');
                });

                input.addEventListener('blur', function() {
                    if (!this.value) {
                        this.parentElement.classList.remove('focused');
                    }
                });
            });

            // مؤشر قوة كلمة المرور
            const passwordInput = document.getElementById('password');
            const strengthFill = document.getElementById('strengthFill');
            const strengthText = document.getElementById('strengthText');

            passwordInput.addEventListener('input', function() {
                const result = checkPasswordStrength(this.value);
                strengthFill.style.width = result.strength + '%';
                strengthFill.style.backgroundColor = result.color;
                strengthText.textContent = result.text;
                strengthText.style.color = result.color;
            });

            // تأثير الأشكال المتحركة
            const shapes = document.querySelectorAll('.shape');
            shapes.forEach((shape, index) => {
                shape.style.animationDelay = `${index * 0.3}s`;
            });

            // التحقق من تطابق كلمات المرور
            const confirmPassword = document.getElementById('confirm_password');
            confirmPassword.addEventListener('input', function() {
                if (this.value && this.value !== passwordInput.value) {
                    this.style.borderColor = '#ef4444';
                } else {
                    this.style.borderColor = '';
                }
            });
        });
    </script>

    <style>
        /* تصميم صفحة التسجيل */
        .register-page {
            background: var(--gradient-light);
            min-height: 100vh;
            overflow-x: hidden;
            position: relative;
        }

        .register-wrapper {
            display: grid;
            grid-template-columns: 1fr 1fr;
            min-height: 100vh;
            max-width: 1400px;
            margin: 0 auto;
        }

        .info-section {
            background: var(--gradient-ocean);
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            padding: 3rem;
            color: white;
            position: relative;
            overflow: hidden;
        }

        .benefits-list {
            position: relative;
            z-index: 2;
            margin-bottom: 2rem;
        }

        .benefit-item {
            display: flex;
            align-items: center;
            gap: 1.5rem;
            margin-bottom: 1.5rem;
            padding: 1.2rem;
            background: rgba(255,255,255,0.1);
            border-radius: 16px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255,255,255,0.2);
            transition: var(--transition-normal);
        }

        .benefit-item:hover {
            transform: translateX(-10px);
            background: rgba(255,255,255,0.15);
        }

        .benefit-icon {
            font-size: 2rem;
            min-width: 50px;
        }

        .benefit-text h3 {
            font-size: 1.1rem;
            font-weight: 700;
            margin-bottom: 0.3rem;
        }

        .benefit-text p {
            opacity: 0.9;
            line-height: 1.4;
            font-size: 0.9rem;
        }

        .testimonial {
            background: rgba(255,255,255,0.1);
            border-radius: 20px;
            padding: 2rem;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255,255,255,0.2);
            position: relative;
            z-index: 2;
        }

        .testimonial-content p {
            font-size: 1.1rem;
            font-style: italic;
            margin-bottom: 1rem;
            line-height: 1.6;
        }

        .testimonial-author strong {
            display: block;
            font-size: 1rem;
            margin-bottom: 0.2rem;
        }

        .testimonial-author span {
            font-size: 0.9rem;
            opacity: 0.8;
        }

        .register-section {
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 2rem;
            background: var(--white);
        }

        .register-container {
            width: 100%;
            max-width: 500px;
        }

        .register-header {
            text-align: center;
            margin-bottom: 2rem;
        }

        .register-title {
            font-size: 2.2rem;
            font-weight: 800;
            color: var(--primary-color);
            margin-bottom: 0.5rem;
            background: var(--gradient-primary);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .register-subtitle {
            color: var(--gray-color);
            font-size: 1rem;
        }

        .register-form {
            margin-bottom: 2rem;
        }

        .form-row {
            margin-bottom: 1.5rem;
        }

        .password-strength {
            margin-bottom: 1.5rem;
            padding: 1rem;
            background: var(--gradient-light);
            border-radius: 12px;
            border: 1px solid rgba(59, 130, 246, 0.1);
        }

        .strength-label {
            font-size: 0.9rem;
            color: var(--gray-color);
            margin-bottom: 0.5rem;
            font-weight: 600;
        }

        .strength-bar {
            width: 100%;
            height: 8px;
            background: rgba(59, 130, 246, 0.1);
            border-radius: 4px;
            overflow: hidden;
            margin-bottom: 0.5rem;
        }

        .strength-fill {
            height: 100%;
            width: 0%;
            background: #ef4444;
            transition: var(--transition-normal);
            border-radius: 4px;
        }

        .strength-text {
            font-size: 0.8rem;
            font-weight: 600;
            color: #ef4444;
        }

        .btn-register {
            width: 100%;
            padding: 1.2rem;
            font-size: 1.1rem;
            font-weight: 700;
        }

        .register-footer {
            text-align: center;
            margin-bottom: 2rem;
        }

        .login-link {
            color: var(--primary-color);
            text-decoration: none;
            font-weight: 600;
            transition: var(--transition-normal);
        }

        .login-link:hover {
            color: var(--secondary-color);
        }

        .membership-benefits {
            text-align: center;
            padding: 1.5rem;
            background: var(--gradient-light);
            border-radius: 16px;
            border: 1px solid rgba(59, 130, 246, 0.1);
        }

        .membership-benefits h4 {
            color: var(--primary-color);
            margin-bottom: 1rem;
            font-weight: 700;
        }

        .benefits-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 1rem;
        }

        .benefit-badge {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 0.5rem;
            padding: 1rem;
            background: var(--white);
            border-radius: 12px;
            border: 1px solid rgba(59, 130, 246, 0.1);
            transition: var(--transition-normal);
        }

        .benefit-badge:hover {
            transform: translateY(-3px);
            box-shadow: var(--shadow-light);
        }

        .badge-icon {
            font-size: 1.5rem;
        }

        .badge-text {
            font-size: 0.8rem;
            font-weight: 600;
            color: var(--gray-color);
        }

        .terms-link {
            color: var(--primary-color);
            text-decoration: none;
            font-weight: 600;
        }

        .terms-link:hover {
            text-decoration: underline;
        }

        .shape-6 {
            width: 90px;
            height: 90px;
            top: 30%;
            left: 60%;
            animation-delay: 5s;
        }

        /* الاستجابة */
        @media (max-width: 1024px) {
            .register-wrapper {
                grid-template-columns: 1fr;
            }

            .info-section {
                display: none;
            }
        }

        @media (max-width: 768px) {
            .register-section {
                padding: 1rem;
            }

            .register-title {
                font-size: 1.8rem;
            }

            .benefits-grid {
                grid-template-columns: 1fr;
                gap: 0.5rem;
            }
        }
    </style>
</body>
</html>
