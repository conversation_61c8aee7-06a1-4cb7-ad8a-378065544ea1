<?php
session_start();

// التحقق من تسجيل الدخول وصلاحيات المدير
if (!isset($_SESSION['user_id']) || !isset($_SESSION['is_admin']) || !$_SESSION['is_admin']) {
    header("Location: ../index.php");
    exit;
}

$page_title = "لوحة التحكم";
require_once '../includes/config.php';
require_once '../includes/functions.php';
require_once '../includes/logger.php';

// تسجيل دخول المدير إلى لوحة التحكم
log_activity('dashboard_access', 'Admin accessed dashboard');

// تهيئة المتغيرات بقيم افتراضية
$members_count = 0;
$active_members = 0;
$revenue = 0;
$monthly_revenue = 0;
$active_subscriptions = 0;
$expiring_subscriptions = 0;

// استعلامات محسنة للحصول على الإحصائيات مع معالجة الأخطاء
try {
    // استعلام واحد محسن للحصول على إحصائيات الأعضاء
    $members_sql = "SELECT
        COUNT(*) as total_members,
        SUM(CASE WHEN status = 'active' THEN 1 ELSE 0 END) as active_members
        FROM members";
    $members_result = mysqli_query($conn, $members_sql);
    if ($members_result) {
        $members_data = mysqli_fetch_assoc($members_result);
        $members_count = $members_data['total_members'];
        $active_members = $members_data['active_members'];
    }

    // استعلام واحد محسن للحصول على إحصائيات المدفوعات
    $payments_sql = "SELECT
        SUM(CASE WHEN status = 'completed' THEN amount ELSE 0 END) as total_revenue,
        SUM(CASE WHEN status = 'completed' AND MONTH(payment_date) = MONTH(CURDATE()) AND YEAR(payment_date) = YEAR(CURDATE()) THEN amount ELSE 0 END) as monthly_revenue
        FROM payments";
    $payments_result = mysqli_query($conn, $payments_sql);
    if ($payments_result) {
        $payments_data = mysqli_fetch_assoc($payments_result);
        $revenue = $payments_data['total_revenue'] ?? 0;
        $monthly_revenue = $payments_data['monthly_revenue'] ?? 0;
    }

    // استعلام واحد محسن للحصول على إحصائيات الاشتراكات
    $subscriptions_sql = "SELECT
        SUM(CASE WHEN status = 'active' THEN 1 ELSE 0 END) as active_subscriptions,
        SUM(CASE WHEN status = 'active' AND end_date BETWEEN CURDATE() AND DATE_ADD(CURDATE(), INTERVAL 7 DAY) THEN 1 ELSE 0 END) as expiring_subscriptions
        FROM subscriptions";
    $subscriptions_result = mysqli_query($conn, $subscriptions_sql);
    if ($subscriptions_result) {
        $subscriptions_data = mysqli_fetch_assoc($subscriptions_result);
        $active_subscriptions = $subscriptions_data['active_subscriptions'];
        $expiring_subscriptions = $subscriptions_data['expiring_subscriptions'];
    }

} catch (Exception $e) {
    // في حالة الخطأ، استخدم القيم الافتراضية
    error_log("خطأ في جلب الإحصائيات: " . $e->getMessage());
}

$nav_path = '../';
include_once '../includes/header.php';
?>

<div class="dashboard-header">
    <h1>مرحباً بك، <?php echo htmlspecialchars($_SESSION['user_name']); ?></h1>
    <p>هنا يمكنك إدارة النادي والتحكم في الأعضاء والاشتراكات.</p>
</div>

<div class="dashboard-cards">
    <div class="card" onclick="window.location.href='members/'" style="cursor: pointer;">
        <div class="card-icon">
            <i class="fas fa-users"></i>
        </div>
        <h3>إجمالي الأعضاء</h3>
        <div class="number"><?php echo $members_count; ?></div>
        <p>النشطين: <span class="highlight"><?php echo $active_members; ?></span></p>
        <div class="card-footer">
            <small>اضغط لعرض التفاصيل</small>
        </div>
    </div>
    <div class="card" onclick="window.location.href='payments/'" style="cursor: pointer;">
        <div class="card-icon">
            <i class="fas fa-money-bill-wave"></i>
        </div>
        <h3>الإيرادات الشهرية</h3>
        <div class="number" style="color: var(--success-color);"><?php echo number_format($monthly_revenue, 3); ?> د.ل</div>
        <p>الإجمالي: <span class="highlight"><?php echo number_format($revenue, 3); ?> د.ل</span></p>
        <div class="card-footer">
            <small>اضغط لعرض المدفوعات</small>
        </div>
    </div>
    <div class="card" onclick="window.location.href='subscriptions/'" style="cursor: pointer;">
        <div class="card-icon">
            <i class="fas fa-credit-card"></i>
        </div>
        <h3>الاشتراكات النشطة</h3>
        <div class="number"><?php echo $active_subscriptions; ?></div>
        <p>تنتهي قريباً: <span class="highlight" style="color: var(--warning-color);"><?php echo $expiring_subscriptions; ?></span></p>
        <div class="card-footer">
            <small>اضغط لإدارة الاشتراكات</small>
        </div>
    </div>
    <div class="card alert-card" onclick="window.location.href='subscriptions/?filter=expiring'" style="cursor: pointer;">
        <div class="card-icon">
            <i class="fas fa-exclamation-triangle"></i>
        </div>
        <h3>تحتاج متابعة</h3>
        <div class="number" style="color: var(--warning-color);"><?php echo $expiring_subscriptions; ?></div>
        <p>اشتراكات تنتهي خلال 7 أيام</p>
        <div class="card-footer">
            <small>اضغط لعرض الاشتراكات المنتهية</small>
        </div>
    </div>
</div>

<div class="page-header">
    <h2>آخر الأعضاء المسجلين</h2>
    <a href="members/" class="btn">عرض الكل</a>
</div>

<div class="table-container">
    <table>
        <thead>
            <tr>
                <th>الاسم</th>
                <th>البريد الإلكتروني</th>
                <th>رقم الهاتف</th>
                <th>تاريخ الانضمام</th>
                <th>الحالة</th>
            </tr>
        </thead>
        <tbody>
            <?php
            // استعلام محسن مع تحديد الحقول المطلوبة فقط
            $recent_members_sql = "SELECT full_name, email, phone_number, join_date, status
                                  FROM members
                                  ORDER BY join_date DESC
                                  LIMIT 5";
            $recent_members_stmt = $conn->prepare($recent_members_sql);
            $recent_members_stmt->execute();
            $recent_members_result = $recent_members_stmt->get_result();

            if ($recent_members_result && $recent_members_result->num_rows > 0) {
                while ($row = $recent_members_result->fetch_assoc()) {
                    $status_text = match($row['status']) {
                        'active' => 'نشط',
                        'inactive' => 'غير نشط',
                        'suspended' => 'موقوف',
                        default => 'غير محدد'
                    };

                    echo "<tr onclick=\"window.location.href='members/profile.php?id={$row['id']}'\" style=\"cursor: pointer;\">
                        <td>" . htmlspecialchars($row['full_name']) . "</td>
                        <td>" . htmlspecialchars($row['email']) . "</td>
                        <td>" . htmlspecialchars($row['phone_number'] ?? 'غير محدد') . "</td>
                        <td>" . date('Y-m-d', strtotime($row['join_date'])) . "</td>
                        <td><span class='status {$row['status']}'>{$status_text}</span></td>
                    </tr>";
                }
            } else {
                echo "<tr><td colspan='5' style='text-align: center; padding: 20px;'>
                        <i class='fas fa-users' style='font-size: 48px; color: var(--gray-color); margin-bottom: 10px; display: block;'></i>
                        لا توجد أعضاء مسجلين بعد
                      </td></tr>";
            }
            $recent_members_stmt->close();
            ?>
        </tbody>
    </table>
</div>

<style>
.dashboard-cards .card {
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.dashboard-cards .card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

.card-icon {
    position: absolute;
    top: 15px;
    right: 15px;
    font-size: 24px;
    opacity: 0.3;
}

.card-footer {
    margin-top: 10px;
    padding-top: 10px;
    border-top: 1px solid rgba(0,0,0,0.1);
}

.card-footer small {
    color: var(--gray-color);
    font-size: 12px;
}

.highlight {
    font-weight: bold;
    color: var(--primary-color);
}

.alert-card {
    border-left: 4px solid var(--warning-color);
}

.alert-card:hover {
    border-left-color: var(--danger-color);
}

/* تحسين الجدول */
.table-container table tbody tr:hover {
    background-color: rgba(102, 126, 234, 0.05);
}

/* تحسين الاستجابة */
@media (max-width: 768px) {
    .dashboard-cards {
        grid-template-columns: 1fr;
    }

    .card-icon {
        display: none;
    }
}
</style>

<script>
// إضافة تأثيرات تفاعلية
document.addEventListener('DOMContentLoaded', function() {
    // تحديث الإحصائيات كل 30 ثانية
    setInterval(function() {
        // يمكن إضافة AJAX لتحديث الإحصائيات دون إعادة تحميل الصفحة
        console.log('تحديث الإحصائيات...');
    }, 30000);

    // إضافة تأثير loading للبطاقات القابلة للنقر
    const clickableCards = document.querySelectorAll('.card[onclick]');
    clickableCards.forEach(card => {
        card.addEventListener('click', function() {
            this.style.opacity = '0.7';
            setTimeout(() => {
                this.style.opacity = '1';
            }, 200);
        });
    });
});
</script>

<?php include_once '../includes/footer.php'; ?>