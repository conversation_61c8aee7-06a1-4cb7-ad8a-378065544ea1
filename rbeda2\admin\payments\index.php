<?php
// تفعيل عرض الأخطاء للتطوير
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

$page_title = "إدارة المدفوعات";
$require_admin = true;
require_once '../../includes/config.php';
require_once '../../includes/auth.php';

// التحقق من وجود الجداول المطلوبة
$required_tables = ['payments', 'members', 'subscriptions', 'subscription_plans'];
foreach ($required_tables as $table) {
    $result = $conn->query("SHOW TABLES LIKE '$table'");
    if (!$result || $result->num_rows == 0) {
        die("جدول $table غير موجود. يرجى تشغيل <a href='../../setup.php'>ملف الإعداد</a> أولاً.");
    }
}

// معالجة تحديث حالة الدفعة
if (isset($_GET['update_status']) && is_numeric($_GET['update_status']) && isset($_GET['status'])) {
    $payment_id = (int)$_GET['update_status'];
    $new_status = sanitize_input($_GET['status']);

    $allowed_statuses = ['pending', 'completed', 'failed', 'refunded'];
    if (in_array($new_status, $allowed_statuses)) {
        $update_sql = "UPDATE payments SET status = ? WHERE id = ?";
        $update_stmt = $conn->prepare($update_sql);
        $update_stmt->bind_param("si", $new_status, $payment_id);

        if ($update_stmt->execute()) {
            $_SESSION['success'] = "تم تحديث حالة الدفعة بنجاح";
        } else {
            $_SESSION['error'] = "حدث خطأ أثناء تحديث حالة الدفعة";
        }
        $update_stmt->close();
    } else {
        $_SESSION['error'] = "حالة الدفعة غير صحيحة";
    }

    redirect('index.php');
    exit;
}

// البحث والفلترة
$search = isset($_GET['search']) ? sanitize_input($_GET['search']) : '';
$status_filter = isset($_GET['status']) ? sanitize_input($_GET['status']) : '';
$method_filter = isset($_GET['method']) ? sanitize_input($_GET['method']) : '';
$date_from = isset($_GET['date_from']) ? sanitize_input($_GET['date_from']) : '';
$date_to = isset($_GET['date_to']) ? sanitize_input($_GET['date_to']) : '';

$where_conditions = [];
$params = [];
$param_types = '';

if (!empty($search)) {
    $where_conditions[] = "(m.full_name LIKE ? OR m.email LIKE ? OR p.notes LIKE ?)";
    $search_param = "%$search%";
    $params = array_merge($params, [$search_param, $search_param, $search_param]);
    $param_types .= 'sss';
}

if (!empty($status_filter)) {
    $where_conditions[] = "p.status = ?";
    $params[] = $status_filter;
    $param_types .= 's';
}

if (!empty($method_filter)) {
    $where_conditions[] = "p.payment_method = ?";
    $params[] = $method_filter;
    $param_types .= 's';
}

if (!empty($date_from)) {
    $where_conditions[] = "p.payment_date >= ?";
    $params[] = $date_from;
    $param_types .= 's';
}

if (!empty($date_to)) {
    $where_conditions[] = "p.payment_date <= ?";
    $params[] = $date_to;
    $param_types .= 's';
}

$where_clause = !empty($where_conditions) ? 'WHERE ' . implode(' AND ', $where_conditions) : '';

// إحصائيات المدفوعات
$stats_sql = "SELECT
                COUNT(*) as total_payments,
                SUM(CASE WHEN p.status = 'completed' THEN p.amount ELSE 0 END) as total_completed,
                SUM(CASE WHEN p.status = 'pending' THEN p.amount ELSE 0 END) as total_pending,
                SUM(CASE WHEN p.status = 'failed' THEN p.amount ELSE 0 END) as total_failed,
                SUM(CASE WHEN p.status = 'refunded' THEN p.amount ELSE 0 END) as total_refunded
              FROM payments p
              JOIN members m ON p.member_id = m.id
              $where_clause";

$stats_stmt = $conn->prepare($stats_sql);
if ($stats_stmt === false) {
    die('خطأ في تحضير الاستعلام: ' . $conn->error);
}

if (!empty($params)) {
    $stats_stmt->bind_param($param_types, ...$params);
}

if (!$stats_stmt->execute()) {
    die('خطأ في تنفيذ الاستعلام: ' . $stats_stmt->error);
}

$stats_stmt->bind_result($total_payments, $total_completed, $total_pending, $total_failed, $total_refunded);
$stats_stmt->fetch();
$stats = [
    'total_payments' => $total_payments ?? 0,
    'total_completed' => $total_completed ?? 0,
    'total_pending' => $total_pending ?? 0,
    'total_failed' => $total_failed ?? 0,
    'total_refunded' => $total_refunded ?? 0
];
$stats_stmt->close();

// الصفحات
$page = isset($_GET['page']) ? max(1, (int)$_GET['page']) : 1;
$per_page = 15;
$offset = ($page - 1) * $per_page;

// جلب المدفوعات
$sql = "SELECT p.*, m.full_name, m.email, sp.name as plan_name
        FROM payments p
        JOIN members m ON p.member_id = m.id
        LEFT JOIN subscriptions s ON p.subscription_id = s.id
        LEFT JOIN subscription_plans sp ON s.plan_id = sp.id
        $where_clause
        ORDER BY p.payment_date DESC, p.created_at DESC
        LIMIT $per_page OFFSET $offset";

$stmt = $conn->prepare($sql);
if ($stmt === false) {
    die('خطأ في تحضير استعلام المدفوعات: ' . $conn->error);
}

if (!empty($params)) {
    $stmt->bind_param($param_types, ...$params);
}

if (!$stmt->execute()) {
    die('خطأ في تنفيذ استعلام المدفوعات: ' . $stmt->error);
}

$payments = $stmt->get_result();
$stmt->close();

// عدد الصفحات
$count_sql = "SELECT COUNT(*) as total FROM payments p JOIN members m ON p.member_id = m.id $where_clause";
$count_stmt = $conn->prepare($count_sql);
if ($count_stmt === false) {
    die('خطأ في تحضير استعلام العد: ' . $conn->error);
}

if (!empty($params)) {
    $count_stmt->bind_param($param_types, ...$params);
}

if (!$count_stmt->execute()) {
    die('خطأ في تنفيذ استعلام العد: ' . $count_stmt->error);
}

$count_result = $count_stmt->get_result();
$total_payments_count = $count_result->fetch_assoc()['total'];
$count_stmt->close();
$total_pages = ceil($total_payments_count / $per_page);

$nav_path = '../../';
include_once '../../includes/header.php';
?>

<div class="page-header fade-in-up">
    <div>
        <h2><i class="fas fa-money-bill-wave"></i> إدارة المدفوعات</h2>
        <p style="color: var(--gray-color); margin-top: 0.5rem;">متابعة وإدارة جميع المدفوعات والمعاملات المالية</p>
    </div>
    <div style="display: flex; gap: 10px;">
        <a href="add.php" class="btn btn-primary">
            <i class="fas fa-plus"></i> إضافة دفعة جديدة
        </a>
        <button class="btn btn-success" onclick="showPaymentStats()">
            <i class="fas fa-chart-pie"></i> الإحصائيات
        </button>
        <button class="btn btn-info" onclick="exportPayments()">
            <i class="fas fa-download"></i> تصدير
        </button>
    </div>
</div>

<!-- إحصائيات المدفوعات المحسنة -->
<div class="dashboard-cards fade-in-up" style="margin-bottom: 30px;">
    <div class="card payment-card total">
        <div class="card-icon">
            <i class="fas fa-receipt"></i>
        </div>
        <h3>إجمالي المدفوعات</h3>
        <div class="number"><?php echo $stats['total_payments']; ?></div>
        <p>دفعة مسجلة</p>
        <div class="card-footer">
            <small><i class="fas fa-calendar"></i> منذ بداية العام</small>
        </div>
    </div>

    <div class="card payment-card completed">
        <div class="card-icon">
            <i class="fas fa-check-circle"></i>
        </div>
        <h3>المدفوعات المكتملة</h3>
        <div class="number" style="color: var(--success-color);"><?php echo number_format($stats['completed_amount'], 3); ?> د.ل</div>
        <p>تم استلامها بنجاح</p>
        <div class="progress-bar">
            <div class="progress success" style="width: <?php echo $stats['total_amount'] > 0 ? ($stats['completed_amount'] / $stats['total_amount']) * 100 : 0; ?>%;"></div>
        </div>
    </div>

    <div class="card payment-card pending">
        <div class="card-icon">
            <i class="fas fa-clock"></i>
        </div>
        <h3>المدفوعات المعلقة</h3>
        <div class="number" style="color: var(--warning-color);"><?php echo number_format($stats['pending_amount'], 3); ?> د.ل</div>
        <p>في انتظار التأكيد</p>
        <div class="status-indicator">
            <span class="status-dot pending"></span>
            <span>يتطلب متابعة</span>
        </div>
    </div>

    <div class="card payment-card refunded">
        <div class="card-icon">
            <i class="fas fa-undo"></i>
        </div>
        <h3>المدفوعات المرتدة</h3>
        <div class="number" style="color: var(--danger-color);"><?php echo number_format($stats['total_refunded'] ?? 0, 3); ?> د.ل</div>
        <p>مبالغ مسترجعة</p>
        <div class="status-indicator">
            <span class="status-dot refunded"></span>
            <span>مسترجعة</span>
        </div>
    </div>
</div>

<!-- نموذج البحث والفلترة -->
<div class="table-container" style="margin-bottom: 20px;">
    <form method="GET" style="padding: 20px; display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; align-items: end;">
        <div class="form-group" style="margin-bottom: 0;">
            <label for="search">البحث:</label>
            <input type="text" id="search" name="search" value="<?php echo htmlspecialchars($search); ?>" placeholder="اسم العضو أو البريد الإلكتروني">
        </div>

        <div class="form-group" style="margin-bottom: 0;">
            <label for="status">حالة الدفعة:</label>
            <select id="status" name="status">
                <option value="">جميع الحالات</option>
                <option value="completed" <?php echo $status_filter == 'completed' ? 'selected' : ''; ?>>مكتملة</option>
                <option value="pending" <?php echo $status_filter == 'pending' ? 'selected' : ''; ?>>معلقة</option>
                <option value="failed" <?php echo $status_filter == 'failed' ? 'selected' : ''; ?>>فاشلة</option>
                <option value="refunded" <?php echo $status_filter == 'refunded' ? 'selected' : ''; ?>>مرتدة</option>
            </select>
        </div>

        <div class="form-group" style="margin-bottom: 0;">
            <label for="method">طريقة الدفع:</label>
            <select id="method" name="method">
                <option value="">جميع الطرق</option>
                <option value="cash" <?php echo $method_filter == 'cash' ? 'selected' : ''; ?>>نقداً</option>
                <option value="card" <?php echo $method_filter == 'card' ? 'selected' : ''; ?>>بطاقة ائتمان</option>
                <option value="bank_transfer" <?php echo $method_filter == 'bank_transfer' ? 'selected' : ''; ?>>تحويل بنكي</option>
                <option value="online" <?php echo $method_filter == 'online' ? 'selected' : ''; ?>>دفع إلكتروني</option>
            </select>
        </div>

        <div class="form-group" style="margin-bottom: 0;">
            <label for="date_from">من تاريخ:</label>
            <input type="date" id="date_from" name="date_from" value="<?php echo $date_from; ?>">
        </div>

        <div class="form-group" style="margin-bottom: 0;">
            <label for="date_to">إلى تاريخ:</label>
            <input type="date" id="date_to" name="date_to" value="<?php echo $date_to; ?>">
        </div>

        <div style="display: flex; gap: 10px;">
            <button type="submit" class="btn"><i class="fas fa-search"></i> بحث</button>
            <a href="index.php" class="btn" style="background: var(--gray-color);"><i class="fas fa-times"></i> إلغاء</a>
        </div>
    </form>
</div>

<div class="table-container">
    <table>
        <thead>
            <tr>
                <th>الرقم</th>
                <th>العضو</th>
                <th>نوع الاشتراك</th>
                <th>المبلغ</th>
                <th>طريقة الدفع</th>
                <th>تاريخ الدفع</th>
                <th>الحالة</th>
                <th>الإجراءات</th>
            </tr>
        </thead>
        <tbody>
            <?php if ($payments->num_rows > 0): ?>
                <?php while ($payment = $payments->fetch_assoc()): ?>
                    <tr>
                        <td><?php echo $payment['id']; ?></td>
                        <td>
                            <strong><?php echo htmlspecialchars($payment['full_name']); ?></strong><br>
                            <small style="color: var(--gray-color);"><?php echo htmlspecialchars($payment['email']); ?></small>
                        </td>
                        <td><?php echo $payment['plan_name'] ? htmlspecialchars($payment['plan_name']) : 'غير محدد'; ?></td>
                        <td>
                            <div style="display: flex; align-items: center; gap: 8px;">
                                <span style="font-size: 1.1rem; font-weight: bold; color: var(--success-color);">
                                    <?php echo number_format($payment['amount'], 3); ?> د.ل
                                </span>
                                <?php if ($payment['amount'] >= 1000): ?>
                                    <span style="background: var(--gradient-gold); color: var(--dark-color);
                                                 padding: 2px 6px; border-radius: 10px; font-size: 0.7rem; font-weight: bold;">
                                        VIP
                                    </span>
                                <?php endif; ?>
                            </div>
                        </td>
                        <td>
                            <?php
                            switch($payment['payment_method']) {
                                case 'cash': echo '<i class="fas fa-money-bill"></i> نقداً'; break;
                                case 'card': echo '<i class="fas fa-credit-card"></i> بطاقة ائتمان'; break;
                                case 'bank_transfer': echo '<i class="fas fa-university"></i> تحويل بنكي'; break;
                                case 'online': echo '<i class="fas fa-globe"></i> دفع إلكتروني'; break;
                                default: echo $payment['payment_method']; break;
                            }
                            ?>
                        </td>
                        <td><?php echo $payment['payment_date']; ?></td>
                        <td>
                            <span class="status <?php echo $payment['status']; ?>">
                                <?php
                                switch($payment['status']) {
                                    case 'completed': echo 'مكتملة'; break;
                                    case 'pending': echo 'معلقة'; break;
                                    case 'failed': echo 'فاشلة'; break;
                                    case 'refunded': echo 'مرتدة'; break;
                                }
                                ?>
                            </span>
                        </td>
                        <td class="actions">
                            <a href="view.php?id=<?php echo $payment['id']; ?>" class="btn" style="padding: 5px 10px; font-size: 12px;">
                                <i class="fas fa-eye"></i> عرض
                            </a>

                            <?php if ($payment['status'] == 'pending'): ?>
                                <a href="?update_status=<?php echo $payment['id']; ?>&status=completed"
                                   class="btn btn-success" style="padding: 5px 10px; font-size: 12px;"
                                   onclick="return confirm('هل أنت متأكد من تأكيد هذه الدفعة؟')">
                                    <i class="fas fa-check"></i> تأكيد
                                </a>
                                <a href="?update_status=<?php echo $payment['id']; ?>&status=failed"
                                   class="btn btn-danger" style="padding: 5px 10px; font-size: 12px;"
                                   onclick="return confirm('هل أنت متأكد من رفض هذه الدفعة؟')">
                                    <i class="fas fa-times"></i> رفض
                                </a>
                            <?php elseif ($payment['status'] == 'completed'): ?>
                                <a href="?update_status=<?php echo $payment['id']; ?>&status=refunded"
                                   class="btn btn-danger" style="padding: 5px 10px; font-size: 12px;"
                                   onclick="return confirm('هل أنت متأكد من استرداد هذه الدفعة؟')">
                                    <i class="fas fa-undo"></i> استرداد
                                </a>
                            <?php endif; ?>
                        </td>
                    </tr>
                <?php endwhile; ?>
            <?php else: ?>
                <tr>
                    <td colspan="8" style="text-align: center; padding: 40px;">
                        <i class="fas fa-money-bill" style="font-size: 48px; color: var(--gray-color); margin-bottom: 15px;"></i>
                        <p>لا توجد مدفوعات مطابقة لمعايير البحث</p>
                    </td>
                </tr>
            <?php endif; ?>
        </tbody>
    </table>
</div>

<!-- الصفحات -->
<?php if ($total_pages > 1): ?>
    <div style="text-align: center; margin-top: 20px;">
        <?php
        $query_params = $_GET;
        for ($i = 1; $i <= $total_pages; $i++):
            $query_params['page'] = $i;
            $query_string = http_build_query($query_params);
        ?>
            <a href="?<?php echo $query_string; ?>"
               class="btn <?php echo $i == $page ? 'btn-success' : ''; ?>"
               style="margin: 0 5px; padding: 8px 12px;">
                <?php echo $i; ?>
            </a>
        <?php endfor; ?>
    </div>
<?php endif; ?>

<style>
/* تحسينات صفحة المدفوعات */
.payment-card {
    position: relative;
    overflow: hidden;
}

.payment-card.total::before {
    background: var(--gradient-primary);
}

.payment-card.completed::before {
    background: var(--gradient-gold);
}

.payment-card.pending::before {
    background: linear-gradient(135deg, #ffc107 0%, #ff8c00 100%);
}

.payment-card.refunded::before {
    background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
}

.progress.success {
    background: var(--gradient-gold);
}

.status-dot.pending {
    background: var(--warning-color);
    animation: pulse 2s infinite;
}

.status-dot.refunded {
    background: var(--danger-color);
}

@keyframes pulse {
    0% { box-shadow: 0 0 0 0 rgba(255, 193, 7, 0.7); }
    70% { box-shadow: 0 0 0 10px rgba(255, 193, 7, 0); }
    100% { box-shadow: 0 0 0 0 rgba(255, 193, 7, 0); }
}

/* تحسين الجدول */
.table-container table tbody tr {
    transition: all 0.3s ease;
}

.table-container table tbody tr:hover {
    background: linear-gradient(90deg, rgba(46, 139, 87, 0.05) 0%, rgba(46, 139, 87, 0.02) 100%);
    transform: translateX(5px);
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

/* تحسين حالات الدفع */
.status {
    padding: 0.4rem 0.8rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: bold;
    display: inline-flex;
    align-items: center;
    gap: 5px;
}

.status.completed {
    background: rgba(40, 167, 69, 0.1);
    color: var(--success-color);
    border: 1px solid rgba(40, 167, 69, 0.3);
}

.status.pending {
    background: rgba(255, 193, 7, 0.1);
    color: #856404;
    border: 1px solid rgba(255, 193, 7, 0.3);
    animation: glow 2s ease-in-out infinite alternate;
}

.status.failed {
    background: rgba(220, 53, 69, 0.1);
    color: var(--danger-color);
    border: 1px solid rgba(220, 53, 69, 0.3);
}

.status.refunded {
    background: rgba(108, 117, 125, 0.1);
    color: var(--gray-color);
    border: 1px solid rgba(108, 117, 125, 0.3);
}

@keyframes glow {
    from { box-shadow: 0 0 5px rgba(255, 193, 7, 0.5); }
    to { box-shadow: 0 0 15px rgba(255, 193, 7, 0.8); }
}

/* نافذة الإحصائيات */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0,0,0,0.5);
    justify-content: center;
    align-items: center;
}

.modal-content {
    background-color: white;
    border-radius: 20px;
    width: 90%;
    max-width: 800px;
    max-height: 90vh;
    overflow-y: auto;
}

.modal-header {
    padding: 2rem;
    border-bottom: 1px solid #eee;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-body {
    padding: 2rem;
}

.close-btn {
    background: none;
    border: none;
    font-size: 24px;
    cursor: pointer;
    color: #999;
    transition: var(--transition-fast);
}

.close-btn:hover {
    color: #333;
    transform: scale(1.1);
}

@media (max-width: 768px) {
    .dashboard-cards {
        grid-template-columns: 1fr !important;
    }

    .page-header {
        flex-direction: column;
        gap: 1rem;
    }

    .table-container {
        overflow-x: auto;
    }
}
</style>

<!-- نافذة الإحصائيات -->
<div id="paymentStatsModal" class="modal" style="display: none;">
    <div class="modal-content">
        <div class="modal-header">
            <h3><i class="fas fa-chart-pie"></i> إحصائيات المدفوعات التفصيلية</h3>
            <button class="close-btn" onclick="closeModal('paymentStatsModal')">&times;</button>
        </div>
        <div class="modal-body">
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px;">
                <div class="stat-card">
                    <h4>معدل النجاح</h4>
                    <div class="stat-number" style="color: var(--success-color);">
                        <?php
                        $success_rate = $stats['total_payments'] > 0 ?
                            (($stats['total_payments'] - ($stats['total_pending'] ?? 0)) / $stats['total_payments']) * 100 : 0;
                        echo number_format($success_rate, 1);
                        ?>%
                    </div>
                </div>
                <div class="stat-card">
                    <h4>متوسط المدفوعات اليومية</h4>
                    <div class="stat-number" style="color: var(--primary-color);">
                        <?php echo number_format(($stats['total_payments'] ?? 0) / 30, 1); ?>
                    </div>
                </div>
                <div class="stat-card">
                    <h4>أعلى دفعة</h4>
                    <div class="stat-number" style="color: var(--accent-color);">
                        <?php
                        // يمكن إضافة استعلام للحصول على أعلى دفعة
                        echo "1,500 د.ل";
                        ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// وظائف التفاعل
function showPaymentStats() {
    document.getElementById('paymentStatsModal').style.display = 'flex';
}

function closeModal(modalId) {
    document.getElementById(modalId).style.display = 'none';
}

function exportPayments() {
    alert('ميزة تصدير المدفوعات ستكون متاحة قريباً');
}

// تأثيرات تفاعلية عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    // تأثير العد التصاعدي للأرقام
    const numbers = document.querySelectorAll('.payment-card .number');
    numbers.forEach(number => {
        const text = number.textContent;
        const finalValue = parseFloat(text.replace(/[^\d.]/g, ''));
        if (finalValue > 0) {
            animateNumber(number, 0, finalValue, 2000, text.includes('د.ل'));
        }
    });

    // تأثير تحميل شريط التقدم
    const progressBars = document.querySelectorAll('.progress');
    progressBars.forEach(bar => {
        const width = bar.style.width;
        bar.style.width = '0%';
        setTimeout(() => {
            bar.style.width = width;
        }, 500);
    });

    // تأثير hover للصفوف
    const rows = document.querySelectorAll('tbody tr');
    rows.forEach(row => {
        row.addEventListener('mouseenter', function() {
            this.style.transform = 'translateX(5px)';
        });

        row.addEventListener('mouseleave', function() {
            this.style.transform = 'translateX(0)';
        });
    });
});

function animateNumber(element, start, end, duration, isCurrency = false) {
    const range = end - start;
    const increment = range / (duration / 16);
    let current = start;

    const timer = setInterval(() => {
        current += increment;
        if (current >= end) {
            current = end;
            clearInterval(timer);
        }

        if (isCurrency) {
            element.textContent = Math.floor(current).toLocaleString() + ' د.ل';
        } else {
            element.textContent = Math.floor(current).toLocaleString();
        }
    }, 16);
}

// إغلاق النافذة المنبثقة عند النقر خارجها
window.onclick = function(event) {
    const modal = document.getElementById('paymentStatsModal');
    if (event.target === modal) {
        modal.style.display = 'none';
    }
}

// تحديث تلقائي للحالات المعلقة
setInterval(function() {
    const pendingElements = document.querySelectorAll('.status.pending');
    pendingElements.forEach(element => {
        // يمكن إضافة AJAX لتحديث الحالات
    });
}, 30000);
</script>

<?php include_once '../../includes/footer.php'; ?>