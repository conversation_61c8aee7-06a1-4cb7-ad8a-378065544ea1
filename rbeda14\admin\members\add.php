<?php
$page_title = "إضافة عضو جديد";
$require_admin = true;
require_once '../../includes/config.php';
require_once '../../includes/auth.php';
require_once '../../includes/functions.php';

$error = '';
$success = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // التحقق من CSRF token
    if (!verify_csrf_token($_POST['csrf_token'] ?? '')) {
        $error = 'رمز الأمان غير صحيح. يرجى المحاولة مرة أخرى.';
        log_security_event("CSRF token mismatch", "Add member attempt");
    }
    // التحقق من معدل الطلبات
    elseif (!check_rate_limit('add_member', 3, 60)) {
        $error = 'تم تجاوز الحد المسموح من المحاولات. يرجى الانتظار قليلاً.';
    }
    else {
        $name = clean_input($_POST['full_name'], 'string');
        $email = clean_input($_POST['email'], 'email');
        $phone = clean_input($_POST['phone_number'], 'string');
        $password = $_POST['password'] ?? '';
        $confirm_password = $_POST['confirm_password'] ?? '';
        $status = clean_input($_POST['status'], 'string');
        $is_admin = isset($_POST['is_admin']) ? 1 : 0;
        $join_date = clean_input($_POST['join_date'], 'string');

        // التحقق من صحة البيانات
        if (empty($name) || empty($email) || empty($password) || empty($confirm_password) || empty($join_date)) {
            $error = 'جميع الحقول المطلوبة يجب تعبئتها';
        } elseif (!validate_email($email)) {
            $error = 'البريد الإلكتروني غير صحيح';
        } elseif ($password !== $confirm_password) {
            $error = 'كلمتا المرور غير متطابقتين';
        } else {
            // التحقق من قوة كلمة المرور
            $password_errors = validate_password($password);
            if (!empty($password_errors)) {
                $error = implode('<br>', $password_errors);
            } elseif (!in_array($status, ['active', 'inactive', 'suspended'])) {
                $error = 'حالة العضو غير صحيحة';
            } else {
                // التحقق من عدم وجود بريد إلكتروني مكرر
                $check_sql = "SELECT id FROM members WHERE email = ?";
                $check_stmt = $conn->prepare($check_sql);
                $check_stmt->bind_param("s", $email);
                $check_stmt->execute();
                $check_stmt->store_result();

                if ($check_stmt->num_rows > 0) {
                    $error = 'البريد الإلكتروني مسجل مسبقاً';
                } else {
                    // تشفير كلمة المرور
                    $hashed_password = password_hash($password, PASSWORD_DEFAULT);

                    // إدخال العضو الجديد
                    $insert_sql = "INSERT INTO members (full_name, email, password, phone_number, join_date, status, is_admin)
                                  VALUES (?, ?, ?, ?, ?, ?, ?)";
                    $insert_stmt = $conn->prepare($insert_sql);
                    $insert_stmt->bind_param("ssssssi", $name, $email, $hashed_password, $phone, $join_date, $status, $is_admin);

                    if ($insert_stmt->execute()) {
                        $_SESSION['success'] = 'تم إضافة العضو بنجاح';
                        log_security_event("Member added", "Member: $name ($email)");
                        redirect('index.php');
                        exit;
                    } else {
                        $error = 'حدث خطأ أثناء إضافة العضو: ' . $conn->error;
                        log_security_event("Member add failed", "Error: " . $conn->error);
                    }
                    $insert_stmt->close();
                }
                $check_stmt->close();
            }
        }
    }
}

$nav_path = '../../';
include_once '../../includes/header.php';
?>

<div class="page-header">
    <h2>إضافة عضو جديد</h2>
    <a href="index.php" class="btn" style="background: var(--gray-color);"><i class="fas fa-arrow-right"></i> العودة للقائمة</a>
</div>

<div class="form-container">
    <?php if ($error): ?>
        <div class="error"><?= $error ?></div>
    <?php elseif ($success): ?>
        <div class="success"><?= $success ?></div>
    <?php endif; ?>

    <form method="POST">
        <input type="hidden" name="csrf_token" value="<?php echo generate_csrf_token(); ?>">

        <div class="form-group">
            <label for="full_name">الاسم الكامل: *</label>
            <input type="text" id="full_name" name="full_name" value="<?php echo isset($_POST['full_name']) ? htmlspecialchars($_POST['full_name']) : ''; ?>" required>
        </div>

        <div class="form-group">
            <label for="email">البريد الإلكتروني: *</label>
            <input type="email" id="email" name="email" value="<?php echo isset($_POST['email']) ? htmlspecialchars($_POST['email']) : ''; ?>" required>
        </div>

        <div class="form-group">
            <label for="phone_number">رقم الهاتف:</label>
            <input type="tel" id="phone_number" name="phone_number" value="<?php echo isset($_POST['phone_number']) ? htmlspecialchars($_POST['phone_number']) : ''; ?>">
        </div>

        <div class="form-group">
            <label for="join_date">تاريخ الانضمام: *</label>
            <input type="date" id="join_date" name="join_date" value="<?php echo isset($_POST['join_date']) ? $_POST['join_date'] : date('Y-m-d'); ?>" required>
        </div>

        <div class="form-group">
            <label for="status">حالة العضو: *</label>
            <select id="status" name="status" required>
                <option value="active" <?php echo (isset($_POST['status']) && $_POST['status'] == 'active') ? 'selected' : ''; ?>>نشط</option>
                <option value="inactive" <?php echo (isset($_POST['status']) && $_POST['status'] == 'inactive') ? 'selected' : ''; ?>>غير نشط</option>
                <option value="suspended" <?php echo (isset($_POST['status']) && $_POST['status'] == 'suspended') ? 'selected' : ''; ?>>موقوف</option>
            </select>
        </div>

        <div class="form-group">
            <label>
                <input type="checkbox" name="is_admin" value="1" <?php echo (isset($_POST['is_admin']) && $_POST['is_admin']) ? 'checked' : ''; ?>>
                منح صلاحيات المدير
            </label>
            <small style="color: var(--gray-color); display: block; margin-top: 5px;">
                تحذير: منح صلاحيات المدير يعطي العضو إمكانية الوصول الكامل للنظام
            </small>
        </div>

        <div class="form-group">
            <label for="password">كلمة المرور: *</label>
            <input type="password" id="password" name="password" required>
            <small style="color: var(--gray-color); display: block; margin-top: 5px;">
                يجب أن تكون 6 أحرف على الأقل
            </small>
        </div>

        <div class="form-group">
            <label for="confirm_password">تأكيد كلمة المرور: *</label>
            <input type="password" id="confirm_password" name="confirm_password" required>
        </div>

        <button type="submit" class="btn"><i class="fas fa-save"></i> إضافة العضو</button>
    </form>
</div>

<?php include_once '../../includes/footer.php'; ?>