/* تصميم حديث وإبداعي لنادي أفانتي - التصميم الأزرق */

:root {
    /* نظام الألوان الأزرق الحديث */
    --primary-color: #1e3a8a; /* أزرق داكن */
    --primary-light: #3b82f6; /* أزرق متوسط */
    --primary-lighter: #60a5fa; /* أزرق فاتح */
    --secondary-color: #0ea5e9; /* أزرق سماوي */
    --accent-color: #06b6d4; /* أزرق فيروزي */
    --accent-light: #67e8f9; /* فيروزي فاتح */
    --dark-color: #0f172a; /* أزرق داكن جداً */
    --light-color: #f8fafc; /* أبيض مزرق */
    --success-color: #10b981; /* أخضر */
    --warning-color: #f59e0b; /* برتقالي */
    --danger-color: #ef4444; /* أحمر */
    --info-color: #06b6d4; /* فيروزي */
    --gray-color: #64748b; /* رمادي مزرق */
    --white: #ffffff;

    /* تدرجات أزرق حديثة */
    --gradient-primary: linear-gradient(135deg, #1e3a8a 0%, #3b82f6 50%, #60a5fa 100%);
    --gradient-secondary: linear-gradient(135deg, #0ea5e9 0%, #06b6d4 100%);
    --gradient-accent: linear-gradient(135deg, #06b6d4 0%, #67e8f9 100%);
    --gradient-dark: linear-gradient(135deg, #0f172a 0%, #1e293b 100%);
    --gradient-light: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    --gradient-ocean: linear-gradient(135deg, #1e40af 0%, #0ea5e9 50%, #06b6d4 100%);
    --gradient-sky: linear-gradient(135deg, #0284c7 0%, #0ea5e9 50%, #38bdf8 100%);

    /* ظلال محسنة */
    --shadow-light: 0 1px 3px rgba(30, 58, 138, 0.1), 0 1px 2px rgba(30, 58, 138, 0.06);
    --shadow-medium: 0 4px 6px rgba(30, 58, 138, 0.1), 0 2px 4px rgba(30, 58, 138, 0.06);
    --shadow-heavy: 0 10px 15px rgba(30, 58, 138, 0.1), 0 4px 6px rgba(30, 58, 138, 0.05);
    --shadow-xl: 0 20px 25px rgba(30, 58, 138, 0.1), 0 10px 10px rgba(30, 58, 138, 0.04);
    --shadow-blue: 0 4px 14px rgba(59, 130, 246, 0.25);
    --shadow-cyan: 0 4px 14px rgba(6, 182, 212, 0.25);

    /* انتقالات محسنة */
    --transition-fast: all 0.15s cubic-bezier(0.4, 0, 0.2, 1);
    --transition-normal: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    --transition-slow: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
    --transition-bounce: all 0.3s cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

/* إعادة تعيين عام */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Cairo', 'Inter', 'Segoe UI', -apple-system, BlinkMacSystemFont, sans-serif;
    background: var(--gradient-light);
    color: var(--dark-color);
    line-height: 1.7;
    min-height: 100vh;
    direction: rtl;
    text-align: right;
    overflow-x: hidden;
}

/* خلفية متحركة */
body::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background:
        radial-gradient(circle at 20% 80%, rgba(59, 130, 246, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(6, 182, 212, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 40% 40%, rgba(30, 58, 138, 0.05) 0%, transparent 50%);
    z-index: -1;
    animation: backgroundMove 20s ease-in-out infinite;
}

@keyframes backgroundMove {
    0%, 100% { transform: translateX(0) translateY(0); }
    25% { transform: translateX(-20px) translateY(-10px); }
    50% { transform: translateX(20px) translateY(10px); }
    75% { transform: translateX(-10px) translateY(20px); }
}

/* تحسين الهيدر */
.header {
    background: var(--gradient-primary);
    box-shadow: var(--shadow-heavy);
    position: sticky;
    top: 0;
    z-index: 1000;
    backdrop-filter: blur(20px);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg, transparent 0%, rgba(255,255,255,0.1) 50%, transparent 100%);
    animation: headerShine 3s ease-in-out infinite;
}

@keyframes headerShine {
    0%, 100% { transform: translateX(-100%); }
    50% { transform: translateX(100%); }
}

.header .container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1.2rem 2rem;
    max-width: 1400px;
    margin: 0 auto;
    position: relative;
    z-index: 2;
}

.logo {
    color: white;
    font-size: 2rem;
    font-weight: 800;
    text-decoration: none;
    display: flex;
    align-items: center;
    gap: 12px;
    transition: var(--transition-normal);
    text-shadow: 0 2px 4px rgba(0,0,0,0.3);
}

.logo::before {
    content: "💪";
    font-size: 2.5rem;
    filter: drop-shadow(0 2px 4px rgba(0,0,0,0.3));
}

.logo:hover {
    transform: scale(1.05);
    text-shadow: 0 4px 8px rgba(0,0,0,0.4);
}

.nav-links {
    display: flex;
    list-style: none;
    gap: 0.5rem;
}

.nav-links li a {
    color: white;
    text-decoration: none;
    padding: 0.9rem 1.5rem;
    border-radius: 12px;
    transition: var(--transition-bounce);
    display: flex;
    align-items: center;
    gap: 8px;
    position: relative;
    overflow: hidden;
    font-weight: 500;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255,255,255,0.1);
}

.nav-links li a::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: var(--transition-normal);
    z-index: -1;
}

.nav-links li a:hover::before {
    left: 100%;
}

.nav-links li a:hover {
    transform: translateY(-3px) scale(1.05);
    box-shadow: var(--shadow-blue);
    background: rgba(255,255,255,0.1);
}

/* تحسين البطاقات */
.dashboard-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
    gap: 2.5rem;
    margin: 2.5rem 0;
    padding: 0 1rem;
}

.card {
    background: var(--white);
    border-radius: 24px;
    padding: 2.5rem;
    box-shadow: var(--shadow-medium);
    transition: var(--transition-bounce);
    position: relative;
    overflow: hidden;
    border: 1px solid rgba(59, 130, 246, 0.1);
    backdrop-filter: blur(10px);
}

.card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 5px;
    background: var(--gradient-ocean);
}

.card::after {
    content: '';
    position: absolute;
    top: -50%;
    right: -50%;
    width: 100%;
    height: 100%;
    background: radial-gradient(circle, rgba(59, 130, 246, 0.05) 0%, transparent 70%);
    transition: var(--transition-slow);
    transform: scale(0);
}

.card:hover {
    transform: translateY(-15px) scale(1.03);
    box-shadow: var(--shadow-xl);
    border-color: rgba(59, 130, 246, 0.3);
}

.card:hover::after {
    transform: scale(1);
}

.card .card-icon {
    position: absolute;
    top: 25px;
    right: 25px;
    font-size: 3.5rem;
    opacity: 0.08;
    color: var(--primary-color);
    transition: var(--transition-normal);
}

.card:hover .card-icon {
    opacity: 0.15;
    transform: scale(1.1) rotate(5deg);
}

.card h3 {
    color: var(--primary-color);
    margin-bottom: 1.2rem;
    font-size: 1.2rem;
    font-weight: 700;
    position: relative;
    z-index: 2;
}

.card .number {
    font-size: 3rem;
    font-weight: 900;
    margin-bottom: 0.8rem;
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    position: relative;
    z-index: 2;
    text-shadow: 0 2px 4px rgba(59, 130, 246, 0.1);
}

.card p {
    color: var(--gray-color);
    font-size: 1rem;
    line-height: 1.6;
    position: relative;
    z-index: 2;
}

.card-footer {
    margin-top: 1.5rem;
    padding-top: 1.5rem;
    border-top: 1px solid rgba(59, 130, 246, 0.1);
    font-size: 0.9rem;
    color: var(--gray-color);
    position: relative;
    z-index: 2;
    display: flex;
    align-items: center;
    gap: 8px;
}

.card-footer::before {
    content: "📊";
    font-size: 1rem;
}

/* تحسين الأزرار */
.btn {
    display: inline-flex;
    align-items: center;
    gap: 10px;
    padding: 1rem 2rem;
    border: none;
    border-radius: 16px;
    text-decoration: none;
    font-weight: 600;
    transition: var(--transition-bounce);
    cursor: pointer;
    position: relative;
    overflow: hidden;
    font-size: 1rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    backdrop-filter: blur(10px);
    border: 2px solid transparent;
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
    transition: var(--transition-normal);
    z-index: 1;
}

.btn:hover::before {
    left: 100%;
}

.btn::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background: rgba(255,255,255,0.2);
    border-radius: 50%;
    transition: var(--transition-fast);
    transform: translate(-50%, -50%);
    z-index: 0;
}

.btn:active::after {
    width: 300px;
    height: 300px;
}

.btn span {
    position: relative;
    z-index: 2;
}

.btn-primary {
    background: var(--gradient-primary);
    color: white;
    box-shadow: var(--shadow-blue);
}

.btn-primary:hover {
    background: var(--gradient-ocean);
    border-color: rgba(255,255,255,0.3);
}

.btn-secondary {
    background: var(--gradient-secondary);
    color: white;
    box-shadow: var(--shadow-cyan);
}

.btn-secondary:hover {
    background: var(--gradient-accent);
    border-color: rgba(255,255,255,0.3);
}

.btn-success {
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
    color: white;
    box-shadow: 0 4px 14px rgba(16, 185, 129, 0.25);
}

.btn-warning {
    background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
    color: white;
    box-shadow: 0 4px 14px rgba(245, 158, 11, 0.25);
}

.btn-danger {
    background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
    color: white;
    box-shadow: 0 4px 14px rgba(239, 68, 68, 0.25);
}

.btn-info {
    background: var(--gradient-accent);
    color: white;
    box-shadow: var(--shadow-cyan);
}

.btn-outline {
    background: transparent;
    color: var(--primary-color);
    border-color: var(--primary-color);
    backdrop-filter: blur(10px);
}

.btn-outline:hover {
    background: var(--primary-color);
    color: white;
}

.btn:hover {
    transform: translateY(-4px) scale(1.05);
    box-shadow: var(--shadow-xl);
}

.btn:active {
    transform: translateY(-2px) scale(1.02);
}

/* تحسين الجداول */
.table-container {
    background: var(--white);
    border-radius: 24px;
    box-shadow: var(--shadow-medium);
    overflow: hidden;
    margin: 2.5rem 0;
    border: 1px solid rgba(59, 130, 246, 0.1);
    backdrop-filter: blur(10px);
}

table {
    width: 100%;
    border-collapse: collapse;
}

thead {
    background: var(--gradient-primary);
    color: white;
    position: relative;
}

thead::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.5), transparent);
}

thead th {
    padding: 1.5rem 1.2rem;
    text-align: right;
    font-weight: 700;
    font-size: 1rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    position: relative;
}

thead th::after {
    content: '';
    position: absolute;
    bottom: 0;
    right: 0;
    width: 1px;
    height: 50%;
    background: rgba(255,255,255,0.2);
}

thead th:last-child::after {
    display: none;
}

tbody tr {
    transition: var(--transition-normal);
    border-bottom: 1px solid rgba(59, 130, 246, 0.08);
    position: relative;
}

tbody tr::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 0;
    height: 100%;
    background: var(--gradient-accent);
    transition: var(--transition-fast);
    opacity: 0.1;
}

tbody tr:hover {
    background: linear-gradient(90deg, rgba(59, 130, 246, 0.03) 0%, rgba(6, 182, 212, 0.02) 100%);
    transform: translateX(5px);
    box-shadow: var(--shadow-light);
}

tbody tr:hover::before {
    width: 4px;
}

tbody td {
    padding: 1.2rem;
    font-size: 1rem;
    position: relative;
    z-index: 1;
}

tbody tr:nth-child(even) {
    background: rgba(59, 130, 246, 0.02);
}

/* تحسين النماذج */
.form-group {
    margin-bottom: 2rem;
    position: relative;
}

.form-group label {
    display: block;
    margin-bottom: 0.8rem;
    color: var(--primary-color);
    font-weight: 600;
    font-size: 1rem;
    position: relative;
}

.form-group label::after {
    content: '';
    position: absolute;
    bottom: -4px;
    right: 0;
    width: 0;
    height: 2px;
    background: var(--gradient-primary);
    transition: var(--transition-normal);
}

.form-group:focus-within label::after {
    width: 100%;
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 1rem 1.2rem;
    border: 2px solid rgba(59, 130, 246, 0.2);
    border-radius: 16px;
    font-size: 1rem;
    transition: var(--transition-bounce);
    background: var(--white);
    backdrop-filter: blur(10px);
    position: relative;
}

.form-group input::placeholder,
.form-group textarea::placeholder {
    color: var(--gray-color);
    opacity: 0.7;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 4px rgba(59, 130, 246, 0.1);
    transform: translateY(-3px) scale(1.02);
    background: var(--white);
}

.form-floating {
    position: relative;
}

.form-floating input {
    padding-top: 1.5rem;
}

.form-floating label {
    position: absolute;
    top: 1rem;
    right: 1.2rem;
    transition: var(--transition-normal);
    pointer-events: none;
    color: var(--gray-color);
}

.form-floating input:focus + label,
.form-floating input:not(:placeholder-shown) + label {
    top: 0.3rem;
    font-size: 0.8rem;
    color: var(--primary-color);
    font-weight: 600;
}

/* تحسين الرسائل */
.alert {
    padding: 1.2rem 1.8rem;
    border-radius: 16px;
    margin: 1.5rem 0;
    border-right: 5px solid;
    display: flex;
    align-items: center;
    gap: 12px;
    backdrop-filter: blur(10px);
    position: relative;
    overflow: hidden;
}

.alert::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(45deg, transparent 30%, rgba(255,255,255,0.1) 50%, transparent 70%);
    animation: alertShine 2s ease-in-out infinite;
}

@keyframes alertShine {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

.alert-success {
    background: rgba(16, 185, 129, 0.1);
    border-right-color: var(--success-color);
    color: var(--success-color);
}

.alert-success::after {
    content: "✅";
    font-size: 1.2rem;
}

.alert-error {
    background: rgba(239, 68, 68, 0.1);
    border-right-color: var(--danger-color);
    color: var(--danger-color);
}

.alert-error::after {
    content: "❌";
    font-size: 1.2rem;
}

.alert-warning {
    background: rgba(245, 158, 11, 0.1);
    border-right-color: var(--warning-color);
    color: #92400e;
}

.alert-warning::after {
    content: "⚠️";
    font-size: 1.2rem;
}

.alert-info {
    background: rgba(6, 182, 212, 0.1);
    border-right-color: var(--info-color);
    color: var(--info-color);
}

.alert-info::after {
    content: "ℹ️";
    font-size: 1.2rem;
}

/* تحسين الصفحات */
.page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin: 2.5rem 0;
    padding: 2.5rem;
    background: var(--white);
    border-radius: 24px;
    box-shadow: var(--shadow-medium);
    border: 1px solid rgba(59, 130, 246, 0.1);
    position: relative;
    overflow: hidden;
}

.page-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--gradient-ocean);
}

.page-header h2 {
    color: var(--primary-color);
    font-size: 2.2rem;
    font-weight: 800;
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* كونتينر رئيسي */
.container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 2rem;
}

.main-content {
    padding: 2rem 0;
    min-height: calc(100vh - 200px);
}

/* بطاقات إحصائيات محسنة */
.stats-card {
    background: var(--white);
    border-radius: 20px;
    padding: 2rem;
    text-align: center;
    box-shadow: var(--shadow-medium);
    transition: var(--transition-bounce);
    border: 1px solid rgba(59, 130, 246, 0.1);
    position: relative;
    overflow: hidden;
}

.stats-card::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: conic-gradient(from 0deg, transparent, rgba(59, 130, 246, 0.05), transparent);
    animation: rotate 10s linear infinite;
}

@keyframes rotate {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.stats-card:hover {
    transform: translateY(-10px) scale(1.05);
    box-shadow: var(--shadow-xl);
}

.stats-number {
    font-size: 3rem;
    font-weight: 900;
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    position: relative;
    z-index: 2;
}

.stats-label {
    color: var(--gray-color);
    font-size: 1.1rem;
    font-weight: 600;
    margin-top: 0.5rem;
    position: relative;
    z-index: 2;
}

/* تحسين الاستجابة */
@media (max-width: 1024px) {
    .container {
        padding: 0 1.5rem;
    }

    .dashboard-cards {
        grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
        gap: 2rem;
    }
}

@media (max-width: 768px) {
    .header .container {
        flex-direction: column;
        gap: 1.5rem;
        padding: 1rem;
    }

    .nav-links {
        flex-wrap: wrap;
        justify-content: center;
        gap: 0.5rem;
    }

    .nav-links li a {
        padding: 0.7rem 1rem;
        font-size: 0.9rem;
    }

    .dashboard-cards {
        grid-template-columns: 1fr;
        gap: 1.5rem;
        padding: 0;
    }

    .page-header {
        flex-direction: column;
        gap: 1.5rem;
        text-align: center;
        padding: 2rem;
    }

    .page-header h2 {
        font-size: 1.8rem;
    }

    .card .number {
        font-size: 2.5rem;
    }

    .btn {
        padding: 0.8rem 1.5rem;
        font-size: 0.9rem;
    }

    .container {
        padding: 0 1rem;
    }
}

@media (max-width: 480px) {
    .card {
        padding: 1.5rem;
    }

    .card .number {
        font-size: 2rem;
    }

    .page-header {
        padding: 1.5rem;
    }

    .page-header h2 {
        font-size: 1.5rem;
    }

    .nav-links li a {
        padding: 0.6rem 0.8rem;
        font-size: 0.8rem;
    }
}

/* تأثيرات خاصة محسنة */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(40px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeInRight {
    from {
        opacity: 0;
        transform: translateX(-40px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes fadeInLeft {
    from {
        opacity: 0;
        transform: translateX(40px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes pulse {
    0%, 100% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
}

.fade-in-up {
    animation: fadeInUp 0.8s cubic-bezier(0.4, 0, 0.2, 1);
}

.fade-in-right {
    animation: fadeInRight 0.8s cubic-bezier(0.4, 0, 0.2, 1);
}

.fade-in-left {
    animation: fadeInLeft 0.8s cubic-bezier(0.4, 0, 0.2, 1);
}

.pulse {
    animation: pulse 2s ease-in-out infinite;
}

/* تحسين شريط التمرير */
::-webkit-scrollbar {
    width: 12px;
}

::-webkit-scrollbar-track {
    background: rgba(59, 130, 246, 0.1);
    border-radius: 10px;
}

::-webkit-scrollbar-thumb {
    background: var(--gradient-primary);
    border-radius: 10px;
    border: 2px solid transparent;
    background-clip: content-box;
}

::-webkit-scrollbar-thumb:hover {
    background: var(--gradient-ocean);
    background-clip: content-box;
}

/* تحسين التركيز */
*:focus {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
}

/* تحسين النصوص */
.text-gradient {
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.text-shadow {
    text-shadow: 0 2px 4px rgba(30, 58, 138, 0.3);
}

/* تحسين الحدود */
.border-gradient {
    border: 2px solid transparent;
    background: linear-gradient(white, white) padding-box,
                var(--gradient-primary) border-box;
}

/* تأثيرات تحميل */
.loading {
    position: relative;
    overflow: hidden;
}

.loading::after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(59, 130, 246, 0.2), transparent);
    animation: loading 1.5s ease-in-out infinite;
}

@keyframes loading {
    0% { left: -100%; }
    100% { left: 100%; }
}
