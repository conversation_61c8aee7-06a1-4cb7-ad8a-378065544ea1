// وظائف جافاسكريبت الأساسية

// تنبيهات الرسائل
document.addEventListener('DOMContentLoaded', function() {
    // إخفاء الرسائل بعد 5 ثوانٍ
    setTimeout(function() {
        const messages = document.querySelectorAll('.error, .success, .warning');
        messages.forEach(message => {
            message.style.opacity = '0';
            setTimeout(() => message.remove(), 500);
        });
    }, 5000);
    
    // تأكيد الحذف
    const deleteButtons = document.querySelectorAll('.btn-danger');
    deleteButtons.forEach(button => {
        button.addEventListener('click', function(e) {
            if (!confirm('هل أنت متأكد من أنك تريد الحذف؟ هذا الإجراء لا يمكن التراجع عنه.')) {
                e.preventDefault();
            }
        });
    });
    
    // تحسين تجربة النماذج
    const inputs = document.querySelectorAll('input, select, textarea');
    inputs.forEach(input => {
        input.addEventListener('focus', function() {
            this.parentElement.classList.add('focused');
        });
        
        input.addEventListener('blur', function() {
            this.parentElement.classList.remove('focused');
        });
    });
});