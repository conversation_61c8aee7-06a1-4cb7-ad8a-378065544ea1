/* التنسيقات العامة */
:root {
    --primary-color: #3498db;
    --secondary-color: #2c3e50;
    --success-color: #27ae60;
    --error-color: #e74c3c;
    --warning-color: #f39c12;
    --light-color: #ecf0f1;
    --dark-color: #34495e;
    --gray-color: #95a5a6;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

body {
    background-color: #f5f7fa;
    color: #333;
    line-height: 1.6;
    direction: rtl;
}

a {
    text-decoration: none;
    color: var(--primary-color);
}

a:hover {
    text-decoration: underline;
}

.container {
    width: 90%;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 15px;
}

.btn {
    display: inline-block;
    background: var(--primary-color);
    color: white;
    padding: 10px 20px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 16px;
    transition: background 0.3s ease;
}

.btn:hover {
    background: #2980b9;
    text-decoration: none;
}

.btn-danger {
    background: var(--error-color);
}

.btn-danger:hover {
    background: #c0392b;
}

.btn-success {
    background: var(--success-color);
}

.btn-success:hover {
    background: #219653;
}

/* تنسيقات الرسائل */
.error {
    background-color: #ffdddd;
    border-left: 4px solid var(--error-color);
    padding: 10px 15px;
    margin: 15px 0;
    border-radius: 0 4px 4px 0;
}

.success {
    background-color: #ddffdd;
    border-left: 4px solid var(--success-color);
    padding: 10px 15px;
    margin: 15px 0;
    border-radius: 0 4px 4px 0;
}

.warning {
    background-color: #fff8dd;
    border-left: 4px solid var(--warning-color);
    padding: 10px 15px;
    margin: 15px 0;
    border-radius: 0 4px 4px 0;
}

/* تنسيقات النماذج */
.form-container {
    max-width: 500px;
    margin: 50px auto;
    background: white;
    padding: 30px;
    border-radius: 8px;
    box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
}

.form-container h2 {
    text-align: center;
    margin-bottom: 20px;
    color: var(--secondary-color);
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
}

.form-group input[type="text"],
.form-group input[type="email"],
.form-group input[type="password"],
.form-group input[type="tel"],
.form-group input[type="date"],
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 10px 15px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 16px;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    border-color: var(--primary-color);
    outline: none;
    box-shadow: 0 0 5px rgba(52, 152, 219, 0.3);
}

/* تنسيقات شريط التنقل */
.navbar {
    background-color: var(--secondary-color);
    color: white;
    padding: 15px 0;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.navbar .container {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.logo {
    font-size: 24px;
    font-weight: bold;
    color: white;
}

.nav-links {
    display: flex;
    list-style: none;
}

.nav-links li {
    margin-left: 20px;
}

.nav-links a {
    color: white;
    font-weight: 500;
}

.nav-links a:hover {
    color: var(--primary-color);
    text-decoration: none;
}

/* تنسيقات لوحة التحكم */
.dashboard-header {
    background: white;
    padding: 20px;
    border-radius: 8px;
    margin-bottom: 30px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.dashboard-header h1 {
    color: var(--secondary-color);
    margin-bottom: 10px;
}

.dashboard-cards {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.card {
    background: white;
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    text-align: center;
}

.card h3 {
    color: var(--secondary-color);
    margin-bottom: 10px;
}

.card .number {
    font-size: 32px;
    font-weight: bold;
    color: var(--primary-color);
}

/* تنسيقات الجداول */
.table-container {
    background: white;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

table {
    width: 100%;
    border-collapse: collapse;
}

th, td {
    padding: 12px 15px;
    text-align: right;
    border-bottom: 1px solid #eee;
}

th {
    background-color: #f8f9fa;
    font-weight: 600;
    color: var(--secondary-color);
}

tr:hover {
    background-color: #f9f9f9;
}

.actions {
    display: flex;
    gap: 10px;
}

/* تنسيقات الصفحات الداخلية */
.page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
}

.page-header h2 {
    color: var(--secondary-color);
}

/* تنسيقات لوحة تحكم العضو */
.member-dashboard .card {
    text-align: right;
}

.profile-info {
    background: white;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    margin-bottom: 30px;
}

.profile-info h3 {
    margin-bottom: 20px;
    color: var(--secondary-color);
    border-bottom: 1px solid #eee;
    padding-bottom: 10px;
}

.info-row {
    display: flex;
    margin-bottom: 15px;
}

.info-label {
    width: 150px;
    font-weight: 600;
    color: var(--dark-color);
}

.info-value {
    flex: 1;
}

/* تنسيقات للهواتف */
@media (max-width: 768px) {
    .dashboard-cards {
        grid-template-columns: 1fr;
    }
    
    .navbar .container {
        flex-direction: column;
        text-align: center;
    }
    
    .nav-links {
        margin-top: 15px;
        justify-content: center;
    }
    
    .nav-links li {
        margin: 0 10px;
    }
    
    .page-header {
        flex-direction: column;
        align-items: flex-start;
    }
    
    .page-header .btn {
        margin-top: 15px;
    }
}
/* بطاقات خطط الاشتراك */
.subscription-plans {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 30px;
    margin-bottom: 40px;
}

.plan-card {
    position: relative;
    background: white;
    border-radius: 8px;
    padding: 30px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    text-align: center;
    border: 1px solid #eee;
    transition: transform 0.3s ease;
}

.plan-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.plan-card h3 {
    color: var(--secondary-color);
    margin-bottom: 15px;
    font-size: 24px;
}

.plan-card .price {
    font-size: 32px;
    font-weight: bold;
    color: var(--primary-color);
    margin-bottom: 20px;
}

.plan-card .discount {
    background: var(--success-color);
    color: white;
    padding: 5px 10px;
    border-radius: 20px;
    display: inline-block;
    margin-bottom: 15px;
    font-size: 14px;
}

.plan-card ul {
    list-style: none;
    margin: 20px 0;
    padding: 0;
    text-align: right;
}

.plan-card ul li {
    padding: 8px 0;
    border-bottom: 1px dashed #eee;
}

.plan-card.popular {
    border: 2px solid var(--primary-color);
    transform: scale(1.05);
}

.plan-card.popular:hover {
    transform: scale(1.05) translateY(-5px);
}

.popular-badge {
    position: absolute;
    top: -12px;
    left: 50%;
    transform: translateX(-50%);
    background: var(--primary-color);
    color: white;
    padding: 5px 20px;
    border-radius: 20px;
    font-size: 14px;
    font-weight: bold;
}

/* حالة الاشتراك */
.status {
    display: inline-block;
    padding: 5px 15px;
    border-radius: 20px;
    font-size: 14px;
    font-weight: 500;
}

.status.active {
    background-color: #d4edda;
    color: #155724;
}

.status.inactive {
    background-color: #f8d7da;
    color: #721c24;
}

.status.suspended {
    background-color: #fff3cd;
    color: #856404;
}

/* بطاقة حالة الاشتراك في لوحة العضو */
.dashboard-cards .status {
    display: block;
    margin: 10px auto;
    padding: 8px 20px;
    font-size: 18px;
    max-width: 120px;
}

/* تنسيقات صفحة تسجيل الدخول والتسجيل */
.login-container, .register-container {
    max-width: 400px;
    margin: 100px auto;
    background: white;
    padding: 40px;
    border-radius: 10px;
    box-shadow: 0 0 30px rgba(0, 0, 0, 0.1);
    text-align: center;
}

.login-container h2, .register-container h2 {
    color: var(--secondary-color);
    margin-bottom: 30px;
    font-size: 28px;
}