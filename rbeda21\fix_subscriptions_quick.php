<?php
// إصلاح سريع لصفحة الاشتراكات
require_once 'includes/config.php';

echo "<!DOCTYPE html>";
echo "<html lang='ar' dir='rtl'>";
echo "<head><meta charset='UTF-8'><title>إصلاح سريع - الاشتراكات</title>";
echo "<style>body{font-family:Arial;padding:20px;background:#f8f9fa;}.container{max-width:800px;margin:0 auto;background:white;padding:20px;border-radius:8px;}.success{color:#27ae60;background:rgba(39,174,96,0.1);padding:10px;border-radius:4px;margin:10px 0;}.error{color:#e74c3c;background:rgba(231,76,60,0.1);padding:10px;border-radius:4px;margin:10px 0;}.btn{background:#4a90e2;color:white;padding:10px 20px;text-decoration:none;border-radius:4px;margin:5px;}</style>";
echo "</head><body>";

echo "<div class='container'>";
echo "<h1>🔧 إصلاح سريع لصفحة الاشتراكات</h1>";

// حذف الجداول القديمة
$conn->query("DROP TABLE IF EXISTS subscriptions");
$conn->query("DROP TABLE IF EXISTS subscription_plans");

// إنشاء جدول خطط الاشتراك
$create_plans = "
CREATE TABLE subscription_plans (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    duration_months INT NOT NULL,
    price DECIMAL(10,3) NOT NULL DEFAULT 0.000,
    discount_percentage DECIMAL(5,2) DEFAULT 0.00,
    is_active TINYINT(1) DEFAULT 1,
    features TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
)";

if ($conn->query($create_plans)) {
    echo "<div class='success'>✅ تم إنشاء جدول خطط الاشتراك</div>";
} else {
    echo "<div class='error'>❌ خطأ: " . $conn->error . "</div>";
}

// إنشاء جدول الاشتراكات مع العمود plan_id
$create_subscriptions = "
CREATE TABLE subscriptions (
    id INT AUTO_INCREMENT PRIMARY KEY,
    member_id INT NOT NULL,
    plan_id INT NOT NULL,
    start_date DATE NOT NULL,
    end_date DATE NOT NULL,
    status ENUM('active', 'expired', 'cancelled', 'suspended') DEFAULT 'active',
    payment_status ENUM('pending', 'paid', 'overdue') DEFAULT 'pending',
    amount_paid DECIMAL(10,3) DEFAULT 0.000,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (member_id) REFERENCES members(id) ON DELETE CASCADE,
    FOREIGN KEY (plan_id) REFERENCES subscription_plans(id) ON DELETE CASCADE
)";

if ($conn->query($create_subscriptions)) {
    echo "<div class='success'>✅ تم إنشاء جدول الاشتراكات مع العمود plan_id</div>";
} else {
    echo "<div class='error'>❌ خطأ: " . $conn->error . "</div>";
}

// إضافة خطط تجريبية
$plans = [
    ['شهري أساسي', 'اشتراك شهري للمبتدئين', 1, 50.000, 0, 'دخول للنادي، الأجهزة الأساسية'],
    ['ربع سنوي', 'اشتراك ثلاثة أشهر', 3, 135.000, 10, 'دخول للنادي، جميع الأجهزة، تدريب شخصي'],
    ['نصف سنوي', 'اشتراك ستة أشهر', 6, 240.000, 20, 'دخول للنادي، جميع الأجهزة، تدريب شخصي، برنامج غذائي'],
    ['سنوي VIP', 'اشتراك سنوي كامل', 12, 400.000, 30, 'جميع المميزات، متابعة طبية، مساج علاجي']
];

foreach ($plans as $plan) {
    $insert = "INSERT INTO subscription_plans (name, description, duration_months, price, discount_percentage, features) 
               VALUES (?, ?, ?, ?, ?, ?)";
    $stmt = $conn->prepare($insert);
    $stmt->bind_param("ssidds", $plan[0], $plan[1], $plan[2], $plan[3], $plan[4], $plan[5]);
    
    if ($stmt->execute()) {
        echo "<div class='success'>✅ تم إضافة خطة: {$plan[0]}</div>";
    } else {
        echo "<div class='error'>❌ خطأ في إضافة {$plan[0]}</div>";
    }
    $stmt->close();
}

// إضافة اشتراكات تجريبية
$members = $conn->query("SELECT id FROM members LIMIT 3");
if ($members && $members->num_rows > 0) {
    $plan_ids = [];
    $plans_result = $conn->query("SELECT id FROM subscription_plans");
    while ($p = $plans_result->fetch_assoc()) {
        $plan_ids[] = $p['id'];
    }
    
    $count = 0;
    while ($member = $members->fetch_assoc() && $count < 3) {
        $plan_id = $plan_ids[array_rand($plan_ids)];
        $start_date = date('Y-m-d');
        $end_date = date('Y-m-d', strtotime('+1 month'));
        
        $insert_sub = "INSERT INTO subscriptions (member_id, plan_id, start_date, end_date, status, payment_status, amount_paid) 
                       VALUES (?, ?, ?, ?, 'active', 'paid', 50.000)";
        $stmt = $conn->prepare($insert_sub);
        $stmt->bind_param("iiss", $member['id'], $plan_id, $start_date, $end_date);
        
        if ($stmt->execute()) {
            echo "<div class='success'>✅ تم إضافة اشتراك للعضو {$member['id']}</div>";
            $count++;
        }
        $stmt->close();
    }
}

// عرض الإحصائيات
$plans_count = $conn->query("SELECT COUNT(*) as c FROM subscription_plans")->fetch_assoc()['c'];
$subs_count = $conn->query("SELECT COUNT(*) as c FROM subscriptions WHERE status = 'active'")->fetch_assoc()['c'];

echo "<h2>📊 الإحصائيات:</h2>";
echo "<div class='success'>📋 خطط الاشتراك: $plans_count</div>";
echo "<div class='success'>👥 الاشتراكات النشطة: $subs_count</div>";

echo "<h2>🚀 اختبار الصفحة:</h2>";
echo "<p>تم إصلاح جميع المشاكل:</p>";
echo "<ul>";
echo "<li>✅ إنشاء جدول subscription_plans</li>";
echo "<li>✅ إنشاء جدول subscriptions مع العمود plan_id</li>";
echo "<li>✅ إضافة بيانات تجريبية</li>";
echo "<li>✅ تبسيط الاستعلامات في الكود</li>";
echo "</ul>";

echo "<a href='admin/subscriptions/' class='btn'>اختبار صفحة الاشتراكات</a>";
echo "<a href='admin/dashboard.php' class='btn'>لوحة التحكم</a>";

echo "<hr style='margin:30px 0;'>";
echo "<p style='text-align:center;color:#7f8c8d;'>تم إصلاح صفحة الاشتراكات بنجاح 🎉</p>";

echo "</div></body></html>";
?>
