<?php
// ملف الإعداد السريع لحل مشاكل قاعدة البيانات
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

echo "<!DOCTYPE html>";
echo "<html lang='ar' dir='rtl'>";
echo "<head>";
echo "<meta charset='UTF-8'>";
echo "<meta name='viewport' content='width=device-width, initial-scale=1.0'>";
echo "<title>إعداد نادي أفانتي</title>";
echo "<style>";
echo "body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; margin: 40px; background: #f5f5f5; }";
echo ".container { max-width: 800px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }";
echo ".success { color: #28a745; background: #d4edda; padding: 10px; border-radius: 5px; margin: 10px 0; }";
echo ".error { color: #dc3545; background: #f8d7da; padding: 10px; border-radius: 5px; margin: 10px 0; }";
echo ".info { color: #0c5460; background: #d1ecf1; padding: 10px; border-radius: 5px; margin: 10px 0; }";
echo ".btn { background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; display: inline-block; margin: 10px 5px; }";
echo ".btn:hover { background: #0056b3; }";
echo "table { width: 100%; border-collapse: collapse; margin: 20px 0; }";
echo "th, td { border: 1px solid #ddd; padding: 8px; text-align: right; }";
echo "th { background: #f8f9fa; }";
echo "</style>";
echo "</head>";
echo "<body>";

echo "<div class='container'>";
echo "<h1>🏋️ إعداد نادي أفانتي الرياضي</h1>";

try {
    require_once 'includes/config.php';
    echo "<div class='success'>✓ تم الاتصال بقاعدة البيانات بنجاح</div>";
    
    // قائمة الجداول المطلوبة
    $required_tables = [
        'members' => "CREATE TABLE IF NOT EXISTS members (
            id INT AUTO_INCREMENT PRIMARY KEY,
            full_name VARCHAR(255) NOT NULL,
            email VARCHAR(255) UNIQUE NOT NULL,
            password VARCHAR(255) NOT NULL,
            phone_number VARCHAR(20),
            join_date DATE NOT NULL,
            status ENUM('active', 'inactive', 'suspended') DEFAULT 'active',
            is_admin BOOLEAN DEFAULT FALSE,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        )",
        
        'subscription_plans' => "CREATE TABLE IF NOT EXISTS subscription_plans (
            id INT AUTO_INCREMENT PRIMARY KEY,
            name VARCHAR(255) NOT NULL,
            description TEXT,
            duration_months INT NOT NULL,
            price DECIMAL(10,2) NOT NULL,
            discount_percentage DECIMAL(5,2) DEFAULT 0,
            is_active BOOLEAN DEFAULT TRUE,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        )",
        
        'subscriptions' => "CREATE TABLE IF NOT EXISTS subscriptions (
            id INT AUTO_INCREMENT PRIMARY KEY,
            member_id INT NOT NULL,
            plan_id INT NOT NULL,
            start_date DATE NOT NULL,
            end_date DATE NOT NULL,
            amount_paid DECIMAL(10,2) NOT NULL,
            payment_method ENUM('cash', 'card', 'bank_transfer', 'online') DEFAULT 'cash',
            status ENUM('active', 'expired', 'cancelled') DEFAULT 'active',
            notes TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (member_id) REFERENCES members(id) ON DELETE CASCADE,
            FOREIGN KEY (plan_id) REFERENCES subscription_plans(id) ON DELETE RESTRICT
        )",
        
        'payments' => "CREATE TABLE IF NOT EXISTS payments (
            id INT AUTO_INCREMENT PRIMARY KEY,
            member_id INT NOT NULL,
            subscription_id INT,
            amount DECIMAL(10,2) NOT NULL,
            payment_method ENUM('cash', 'card', 'bank_transfer', 'online') DEFAULT 'cash',
            payment_date DATE NOT NULL,
            status ENUM('pending', 'completed', 'failed', 'refunded') DEFAULT 'completed',
            notes TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (member_id) REFERENCES members(id) ON DELETE CASCADE,
            FOREIGN KEY (subscription_id) REFERENCES subscriptions(id) ON DELETE SET NULL
        )"
    ];
    
    echo "<h2>إنشاء الجداول:</h2>";
    
    foreach ($required_tables as $table_name => $create_sql) {
        if ($conn->query($create_sql)) {
            echo "<div class='success'>✓ جدول $table_name تم إنشاؤه أو موجود بالفعل</div>";
        } else {
            echo "<div class='error'>✗ خطأ في إنشاء جدول $table_name: " . $conn->error . "</div>";
        }
    }
    
    // إدراج البيانات الأساسية
    echo "<h2>إدراج البيانات الأساسية:</h2>";
    
    // خطط الاشتراك
    $plans_sql = "INSERT IGNORE INTO subscription_plans (id, name, description, duration_months, price, discount_percentage) VALUES
        (1, 'اشتراك شهري', 'اشتراك شهري للنادي مع جميع المرافق', 1, 300.00, 0),
        (2, 'اشتراك ربع سنوي', 'اشتراك لثلاثة أشهر مع خصم 10%', 3, 810.00, 10),
        (3, 'اشتراك نصف سنوي', 'اشتراك لستة أشهر مع خصم 15%', 6, 1530.00, 15),
        (4, 'اشتراك سنوي', 'اشتراك سنوي مع خصم 20%', 12, 2880.00, 20)";
    
    if ($conn->query($plans_sql)) {
        echo "<div class='success'>✓ تم إدراج خطط الاشتراك</div>";
    } else {
        echo "<div class='error'>✗ خطأ في إدراج خطط الاشتراك: " . $conn->error . "</div>";
    }
    
    // المدير الافتراضي
    $admin_password = password_hash('admin123', PASSWORD_DEFAULT);
    $admin_sql = "INSERT IGNORE INTO members (id, full_name, email, password, phone_number, join_date, status, is_admin) VALUES
        (1, 'مدير النظام', '<EMAIL>', '$admin_password', '0912345678', CURDATE(), 'active', TRUE)";
    
    if ($conn->query($admin_sql)) {
        echo "<div class='success'>✓ تم إنشاء حساب المدير (<EMAIL> / admin123)</div>";
    } else {
        echo "<div class='error'>✗ خطأ في إنشاء حساب المدير: " . $conn->error . "</div>";
    }
    
    // بيانات تجريبية للمدفوعات
    $payments_sql = "INSERT IGNORE INTO payments (id, member_id, subscription_id, amount, payment_method, payment_date, status, notes) VALUES
        (1, 1, NULL, 300.00, 'cash', CURDATE(), 'completed', 'دفعة تجريبية'),
        (2, 1, NULL, 150.00, 'card', DATE_SUB(CURDATE(), INTERVAL 1 DAY), 'pending', 'دفعة معلقة'),
        (3, 1, NULL, 200.00, 'bank_transfer', DATE_SUB(CURDATE(), INTERVAL 2 DAY), 'completed', 'تحويل بنكي')";
    
    if ($conn->query($payments_sql)) {
        echo "<div class='success'>✓ تم إدراج بيانات تجريبية للمدفوعات</div>";
    } else {
        echo "<div class='error'>✗ خطأ في إدراج بيانات المدفوعات: " . $conn->error . "</div>";
    }
    
    echo "<h2>اختبار النظام:</h2>";
    
    // اختبار استعلام المدفوعات
    $test_sql = "SELECT p.*, m.full_name FROM payments p JOIN members m ON p.member_id = m.id LIMIT 3";
    $result = $conn->query($test_sql);
    
    if ($result && $result->num_rows > 0) {
        echo "<div class='success'>✓ استعلام المدفوعات يعمل بنجاح</div>";
        echo "<table>";
        echo "<tr><th>ID</th><th>العضو</th><th>المبلغ</th><th>التاريخ</th><th>الحالة</th></tr>";
        while ($row = $result->fetch_assoc()) {
            echo "<tr>";
            echo "<td>" . $row['id'] . "</td>";
            echo "<td>" . $row['full_name'] . "</td>";
            echo "<td>" . number_format($row['amount'], 3) . " د.ل</td>";
            echo "<td>" . $row['payment_date'] . "</td>";
            echo "<td>" . $row['status'] . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<div class='error'>✗ لا توجد بيانات في جدول المدفوعات</div>";
    }
    
    echo "<div class='info'>";
    echo "<h3>🎉 تم الإعداد بنجاح!</h3>";
    echo "<p>يمكنك الآن استخدام النظام:</p>";
    echo "<a href='index.php' class='btn'>تسجيل الدخول</a>";
    echo "<a href='admin/payments/' class='btn'>صفحة المدفوعات</a>";
    echo "<a href='admin/dashboard.php' class='btn'>لوحة التحكم</a>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div class='error'>✗ خطأ: " . $e->getMessage() . "</div>";
}

echo "</div>";
echo "</body>";
echo "</html>";

$conn->close();
?>
