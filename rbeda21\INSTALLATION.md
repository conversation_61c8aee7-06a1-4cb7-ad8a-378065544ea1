# دليل تثبيت وتشغيل نظام إدارة نادي أفانتي

## خطوات التثبيت

### 1. إعداد قاعدة البيانات

#### أ. إنشاء قاعدة البيانات
```sql
CREATE DATABASE avantely_club_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
```

#### ب. تشغيل ملف قاعدة البيانات
```bash
mysql -u your_username -p avantely_club_db < database.sql
```

أو يمكنك نسخ محتوى ملف `database.sql` وتشغيله في phpMyAdmin أو أي أداة إدارة قواعد البيانات.

### 2. تحديث إعدادات الاتصال

قم بتحديث ملف `includes/config.php` بمعلومات قاعدة البيانات الخاصة بك:

```php
define('DB_SERVER', 'localhost');
define('DB_USERNAME', 'your_db_username');
define('DB_PASSWORD', 'your_db_password');
define('DB_NAME', 'avantely_club_db');
```

### 3. رفع الملفات

قم برفع جميع ملفات المشروع إلى مجلد الويب الخاص بك (public_html أو www).

### 4. تعيين الصلاحيات

تأكد من أن المجلدات التالية قابلة للكتابة:
```bash
chmod 755 error_log/
chmod 755 member/error_log/
```

## بيانات الدخول الافتراضية

### حساب المدير:
- **البريد الإلكتروني:** <EMAIL>
- **كلمة المرور:** password

### حساب عضو تجريبي:
- **البريد الإلكتروني:** <EMAIL>
- **كلمة المرور:** password

## اختبار النظام

### 1. تسجيل الدخول كمدير
1. اذهب إلى الموقع الخاص بك
2. سجل الدخول بحساب المدير
3. تأكد من ظهور لوحة تحكم المدير بالإحصائيات

### 2. إضافة خطة اشتراك جديدة
1. اذهب إلى "إدارة خطط الاشتراك"
2. اضغط "إضافة خطة جديدة"
3. أدخل البيانات واحفظ

### 3. تسجيل عضو جديد
1. اذهب إلى "إدارة الأعضاء"
2. اضغط "إضافة عضو جديد"
3. أدخل البيانات واحفظ

### 4. اختبار حساب العضو
1. سجل الخروج من حساب المدير
2. سجل الدخول بحساب العضو التجريبي
3. تأكد من ظهور لوحة تحكم العضو

## الميزات المتاحة

### للمديرين:
- ✅ لوحة تحكم شاملة مع الإحصائيات
- ✅ إدارة الأعضاء (إضافة، تعديل، حذف، تفعيل/إيقاف)
- ✅ إدارة خطط الاشتراك والأسعار
- ✅ إدارة المدفوعات والفواتير
- ✅ تقارير مفصلة
- ✅ البحث والفلترة في جميع الأقسام

### للأعضاء:
- ✅ لوحة تحكم شخصية
- ✅ عرض حالة الاشتراك والمدة المتبقية
- ✅ تجديد الاشتراك أو تغيير الخطة
- ✅ إدارة الملف الشخصي
- ✅ تاريخ الاشتراكات والمدفوعات

## استكشاف الأخطاء

### مشكلة الاتصال بقاعدة البيانات
- تأكد من صحة بيانات الاتصال في `includes/config.php`
- تأكد من تشغيل خادم MySQL
- تأكد من وجود قاعدة البيانات

### مشكلة الصفحات لا تظهر بشكل صحيح
- تأكد من رفع جميع الملفات
- تأكد من وجود ملفات CSS و JavaScript
- تحقق من أخطاء PHP في ملفات error_log

### مشكلة تسجيل الدخول
- تأكد من تشغيل ملف database.sql
- تأكد من وجود البيانات الافتراضية
- جرب إعادة تعيين كلمة المرور من قاعدة البيانات

## الأمان

### تغيير كلمات المرور الافتراضية
```sql
-- تغيير كلمة مرور المدير
UPDATE members SET password = '$2y$10$your_new_hashed_password' WHERE email = '<EMAIL>';
```

### تحديث بيانات الاتصال
- غير بيانات قاعدة البيانات في `includes/config.php`
- استخدم كلمات مرور قوية
- فعل HTTPS على الموقع

## الدعم الفني

إذا واجهت أي مشاكل:
1. تحقق من ملفات error_log
2. تأكد من متطلبات النظام (PHP 7.4+, MySQL 5.7+)
3. تأكد من تفعيل extensions المطلوبة (mysqli, session)

## ملاحظات مهمة

- النظام يدعم اللغة العربية بالكامل
- جميع كلمات المرور مشفرة
- النظام محمي ضد SQL Injection
- يمكن تخصيص التصميم من ملف `assets/css/style.css`
