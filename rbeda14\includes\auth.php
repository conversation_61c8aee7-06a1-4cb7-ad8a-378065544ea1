<?php
// ملف التحقق من الجلسة
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

require_once dirname(__FILE__) . '/config.php';
require_once dirname(__FILE__) . '/functions.php';

// إذا لم يكن المستخدم مسجل الدخول، قم بتوجيهه إلى صفحة تسجيل الدخول
if (!is_logged_in()) {
    if (strpos($_SERVER['REQUEST_URI'], '/admin/') !== false) {
        header("Location: ../index.php");
    } elseif (strpos($_SERVER['REQUEST_URI'], '/member/') !== false) {
        header("Location: ../index.php");
    } else {
        header("Location: index.php");
    }
    exit;
}

// التحقق من صلاحيات المسؤول
if (isset($require_admin) && $require_admin && !is_admin()) {
    $_SESSION['error'] = "ليس لديك صلاحيات للوصول لهذه الصفحة";
    if (strpos($_SERVER['REQUEST_URI'], '/admin/') !== false) {
        header("Location: ../member/dashboard.php");
    } else {
        header("Location: member/dashboard.php");
    }
    exit;
}
?>